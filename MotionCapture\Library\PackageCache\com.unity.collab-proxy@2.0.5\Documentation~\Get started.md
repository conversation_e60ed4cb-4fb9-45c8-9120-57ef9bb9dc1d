# Getting started with Unity version control

You can use Unity version control directly in Unity and is available via the Version Control package in the Unity Package Manager.

Learn more about [Unity version control Cloud Edition](https://unity.com/products/plastic-scm).

* To start with a new version control repository for your project, see [Getting started with a new repository](NewPlasticRepo.md).
* To start from an existing Unity version control repository, see [Getting started with an existing repository](ExistingPlasticRepo.md).