using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using System.IO;

public class AnimationCode : MonoBehaviour
{
    [Header("Animation Settings")]
    public GameObject[] Body;
    public float frameRate = 30f;
    public bool autoPlay = true;
    public bool loopAnimation = true;

    [Header("Scaling Settings")]
    public float xScale = 100f;
    public float yScale = 100f;
    public float zScale = 300f;

    [Header("Smoothing")]
    public bool enableSmoothing = true;
    public float smoothingFactor = 0.1f;

    private List<PoseFrame> poseFrames = new List<PoseFrame>();
    private int currentFrame = 0;
    private float frameTimer = 0f;
    private bool isPlaying = false;
    private Vector3[] previousPositions;
    private Vector3[] targetPositions;

    [System.Serializable]
    public class PoseFrame
    {
        public Vector3[] jointPositions;
        public float timestamp;
        public float confidence;

        public PoseFrame(int jointCount)
        {
            jointPositions = new Vector3[jointCount];
        }
    }

    void Start()
    {
        LoadAnimationData();
        InitializePositionArrays();

        if (autoPlay)
        {
            PlayAnimation();
        }
    }

    void Update()
    {
        if (isPlaying && poseFrames.Count > 0)
        {
            UpdateAnimation();
        }
    }

    private void LoadAnimationData()
    {
        string filePath = Path.Combine(Application.dataPath, "AnimationFile.txt");

        if (!File.Exists(filePath))
        {
            Debug.LogError($"Animation file not found at: {filePath}");
            return;
        }

        try
        {
            string[] lines = File.ReadAllLines(filePath);

            for (int i = 0; i < lines.Length; i++)
            {
                if (string.IsNullOrEmpty(lines[i])) continue;

                PoseFrame frame = ParsePoseFrame(lines[i], i);
                if (frame != null)
                {
                    poseFrames.Add(frame);
                }
            }

            Debug.Log($"Loaded {poseFrames.Count} pose frames");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error loading animation data: {e.Message}");
        }
    }

    private PoseFrame ParsePoseFrame(string line, int frameIndex)
    {
        try
        {
            string[] points = line.Split(',');

            if (points.Length < 99) // 33 joints * 3 coordinates
            {
                Debug.LogWarning($"Insufficient data in frame {frameIndex}");
                return null;
            }

            PoseFrame frame = new PoseFrame(33);
            frame.timestamp = frameIndex / frameRate;
            frame.confidence = 1.0f; // Default confidence

            for (int i = 0; i < 33; i++)
            {
                if (i * 3 + 2 < points.Length)
                {
                    float x = ParseFloat(points[i * 3]) / xScale;
                    float y = ParseFloat(points[i * 3 + 1]) / yScale;
                    float z = ParseFloat(points[i * 3 + 2]) / zScale;

                    frame.jointPositions[i] = new Vector3(x, y, z);
                }
            }

            return frame;
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error parsing frame {frameIndex}: {e.Message}");
            return null;
        }
    }

    private float ParseFloat(string value)
    {
        if (float.TryParse(value, out float result))
        {
            return result;
        }
        return 0f;
    }

    private void InitializePositionArrays()
    {
        if (Body != null && Body.Length > 0)
        {
            previousPositions = new Vector3[Body.Length];
            targetPositions = new Vector3[Body.Length];

            for (int i = 0; i < Body.Length; i++)
            {
                if (Body[i] != null)
                {
                    previousPositions[i] = Body[i].transform.localPosition;
                    targetPositions[i] = Body[i].transform.localPosition;
                }
            }
        }
    }

    private void UpdateAnimation()
    {
        frameTimer += Time.deltaTime;
        float frameDuration = 1f / frameRate;

        if (frameTimer >= frameDuration)
        {
            frameTimer = 0f;
            AdvanceFrame();
        }

        // Apply smoothing between frames
        if (enableSmoothing)
        {
            ApplySmoothing();
        }
        else
        {
            ApplyDirectPositions();
        }
    }

    private void AdvanceFrame()
    {
        if (poseFrames.Count == 0) return;

        // Update target positions from current frame
        PoseFrame currentPoseFrame = poseFrames[currentFrame];

        for (int i = 0; i < Body.Length && i < currentPoseFrame.jointPositions.Length; i++)
        {
            if (Body[i] != null)
            {
                targetPositions[i] = currentPoseFrame.jointPositions[i];
            }
        }

        // Advance to next frame
        currentFrame++;
        if (currentFrame >= poseFrames.Count)
        {
            if (loopAnimation)
            {
                currentFrame = 0;
            }
            else
            {
                StopAnimation();
            }
        }
    }

    private void ApplySmoothing()
    {
        for (int i = 0; i < Body.Length; i++)
        {
            if (Body[i] != null)
            {
                Vector3 currentPos = Body[i].transform.localPosition;
                Vector3 smoothedPos = Vector3.Lerp(currentPos, targetPositions[i], smoothingFactor);
                Body[i].transform.localPosition = smoothedPos;
            }
        }
    }

    private void ApplyDirectPositions()
    {
        for (int i = 0; i < Body.Length; i++)
        {
            if (Body[i] != null)
            {
                Body[i].transform.localPosition = targetPositions[i];
            }
        }
    }

    // Public control methods
    public void PlayAnimation()
    {
        isPlaying = true;
        frameTimer = 0f;
    }

    public void StopAnimation()
    {
        isPlaying = false;
    }

    public void PauseAnimation()
    {
        isPlaying = false;
    }

    public void ResetAnimation()
    {
        currentFrame = 0;
        frameTimer = 0f;
    }

    public void SetFrameRate(float newFrameRate)
    {
        frameRate = Mathf.Max(1f, newFrameRate);
    }

    public void SetFrame(int frameIndex)
    {
        if (frameIndex >= 0 && frameIndex < poseFrames.Count)
        {
            currentFrame = frameIndex;
            frameTimer = 0f;
        }
    }

    // Getters for UI
    public int GetCurrentFrame() => currentFrame;
    public int GetTotalFrames() => poseFrames.Count;
    public bool IsPlaying() => isPlaying;
    public float GetAnimationProgress() => poseFrames.Count > 0 ? (float)currentFrame / poseFrames.Count : 0f;
}