{"msg":"init","dagFile":"Library/Bee/1900b0aE.dag","targets":["ScriptAssemblies"]}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"ScriptAssemblies","enqueuedNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_B009007CB866ED03.mvfrm","enqueuedNodeIndex":5,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_4A503D7C9B535AEF.mvfrm","enqueuedNodeIndex":7,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_A2E87A4D8CFE3DDA.mvfrm","enqueuedNodeIndex":8,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_CA3B36F6C19A89E9.mvfrm","enqueuedNodeIndex":9,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_75D831B6A540D53E.mvfrm","enqueuedNodeIndex":10,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_8519B72F30B7EEE3.mvfrm","enqueuedNodeIndex":11,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_84D6A8621C493071.mvfrm","enqueuedNodeIndex":12,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_11DB9A34FA868FF7.mvfrm","enqueuedNodeIndex":13,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_E605164F31FC9E53.mvfrm","enqueuedNodeIndex":14,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_06CF1E61B9937147.mvfrm","enqueuedNodeIndex":15,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_B2E90B5842DD1C21.mvfrm","enqueuedNodeIndex":16,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_7B346C974D1F5AEB.mvfrm","enqueuedNodeIndex":17,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_986F749CC4F33028.mvfrm","enqueuedNodeIndex":18,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_8E8F8EE39FADED60.mvfrm","enqueuedNodeIndex":19,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_53494DA4BD956B35.mvfrm","enqueuedNodeIndex":20,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_CDB1A95E7E145EAF.mvfrm","enqueuedNodeIndex":21,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_FDB4EFCD5ACD4A7E.mvfrm","enqueuedNodeIndex":22,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_7127B1C813DA0DCC.mvfrm","enqueuedNodeIndex":23,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_53D5AD7EC74B882D.mvfrm","enqueuedNodeIndex":24,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_1CB45D079AA45272.mvfrm","enqueuedNodeIndex":25,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_0FE6296F75202CCA.mvfrm","enqueuedNodeIndex":26,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_34FDE67FD72C093F.mvfrm","enqueuedNodeIndex":27,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_09A4870D30E9488B.mvfrm","enqueuedNodeIndex":28,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_D71F57A4BA15FD36.mvfrm","enqueuedNodeIndex":29,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_488457505398C519.mvfrm","enqueuedNodeIndex":30,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_1D12CBD0225DA556.mvfrm","enqueuedNodeIndex":31,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_D8A59059492883B0.mvfrm","enqueuedNodeIndex":32,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_676ABA2C7AEF0941.mvfrm","enqueuedNodeIndex":33,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_424F017654AB0835.mvfrm","enqueuedNodeIndex":34,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_8B6E2D36E99856BA.mvfrm","enqueuedNodeIndex":35,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_71A26EB5B308E8FC.mvfrm","enqueuedNodeIndex":36,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_B98AA527428CA21A.mvfrm","enqueuedNodeIndex":37,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_5347A9488345F9E4.mvfrm","enqueuedNodeIndex":38,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_2A6A94B8015537C3.mvfrm","enqueuedNodeIndex":39,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_D38426C52D066A7D.mvfrm","enqueuedNodeIndex":40,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_5389693E2972069A.mvfrm","enqueuedNodeIndex":41,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_ECF821B937ABA95C.mvfrm","enqueuedNodeIndex":42,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_479158231A9CA7BA.mvfrm","enqueuedNodeIndex":43,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_F2E965C2FA451353.mvfrm","enqueuedNodeIndex":44,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_D130410B56E7FD0E.mvfrm","enqueuedNodeIndex":45,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_9E2287C3B1E222F4.mvfrm","enqueuedNodeIndex":46,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_D7BD4DAE2A87750D.mvfrm","enqueuedNodeIndex":47,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_7B65195D8C536D40.mvfrm","enqueuedNodeIndex":48,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_9BB14A022E6A0C89.mvfrm","enqueuedNodeIndex":49,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_3C49F2A4315F7028.mvfrm","enqueuedNodeIndex":50,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_591B53DA12AB67D2.mvfrm","enqueuedNodeIndex":51,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_C5DA40F663BC1690.mvfrm","enqueuedNodeIndex":52,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_66F4F0C59F5004CE.mvfrm","enqueuedNodeIndex":53,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_128E18DD6FC8C69B.mvfrm","enqueuedNodeIndex":54,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_53CF5C60D786E1BC.mvfrm","enqueuedNodeIndex":55,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_1C885156FB3E8240.mvfrm","enqueuedNodeIndex":56,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_8370AE51D55B6124.mvfrm","enqueuedNodeIndex":57,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_1DBEFCD96369F5BA.mvfrm","enqueuedNodeIndex":58,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_8AC679A0A072FC67.mvfrm","enqueuedNodeIndex":59,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_ECE5B5A1A60989A9.mvfrm","enqueuedNodeIndex":60,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_98F905094D464069.mvfrm","enqueuedNodeIndex":61,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_1B2C760BE566AFA1.mvfrm","enqueuedNodeIndex":62,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_3014A9005AF6E0C6.mvfrm","enqueuedNodeIndex":63,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_447FE10D58FA1CDC.mvfrm","enqueuedNodeIndex":64,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_35660A69CD57B1E7.mvfrm","enqueuedNodeIndex":65,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_F0278B1129FD0781.mvfrm","enqueuedNodeIndex":66,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_F1579CE7AE5A4FEC.mvfrm","enqueuedNodeIndex":67,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_842839871A1EA450.mvfrm","enqueuedNodeIndex":68,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_766A20F7659AD660.mvfrm","enqueuedNodeIndex":69,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_B3648F7EA00CC91E.mvfrm","enqueuedNodeIndex":70,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_E83DAECB7A832050.mvfrm","enqueuedNodeIndex":71,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_F6B877512297B45B.mvfrm","enqueuedNodeIndex":72,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_54E7F0BDC1701621.mvfrm","enqueuedNodeIndex":73,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_F2D9E52A8A1DA43D.mvfrm","enqueuedNodeIndex":74,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_E514777F6F7897AD.mvfrm","enqueuedNodeIndex":75,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_82FE19077176A6E4.mvfrm","enqueuedNodeIndex":76,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_9C617BE0309AD0B2.mvfrm","enqueuedNodeIndex":77,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_5C0BC72BFAE5E8B3.mvfrm","enqueuedNodeIndex":78,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_FC37FAF5BBAF0F0E.mvfrm","enqueuedNodeIndex":79,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_D37778ED904E6F34.mvfrm","enqueuedNodeIndex":80,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_6E3EBF5853783B2F.mvfrm","enqueuedNodeIndex":81,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_1D072DB291F67E28.mvfrm","enqueuedNodeIndex":82,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_B39AE0B6404CFC63.mvfrm","enqueuedNodeIndex":83,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_63A26A7278E8F511.mvfrm","enqueuedNodeIndex":84,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_1D62CA4F9CE86BBD.mvfrm","enqueuedNodeIndex":85,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_BF7F8C87F103B32C.mvfrm","enqueuedNodeIndex":86,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_B64929654506FD14.mvfrm","enqueuedNodeIndex":87,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_FD883EED3964B210.mvfrm","enqueuedNodeIndex":88,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_7AC9B62699C5AFAB.mvfrm","enqueuedNodeIndex":89,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_D953FDA14B6C9CFB.mvfrm","enqueuedNodeIndex":90,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_6D3089BB8D2919C6.mvfrm","enqueuedNodeIndex":91,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_1B89DE15EED7BC0C.mvfrm","enqueuedNodeIndex":92,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_F834F25F3A2ABA1A.mvfrm","enqueuedNodeIndex":93,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_4401640319B4850E.mvfrm","enqueuedNodeIndex":94,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_7DE8E5E14EA73DFF.mvfrm","enqueuedNodeIndex":95,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_6EB9682B6CBB35BC.mvfrm","enqueuedNodeIndex":96,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_F001EA08C606A7C7.mvfrm","enqueuedNodeIndex":97,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_37EA207A558A60E5.mvfrm","enqueuedNodeIndex":98,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_DF889B6FFC3E7D09.mvfrm","enqueuedNodeIndex":99,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_1CE54D6266641F08.mvfrm","enqueuedNodeIndex":100,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_63005792C3CA19A5.mvfrm","enqueuedNodeIndex":101,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_483FC56B1CE0C105.mvfrm","enqueuedNodeIndex":102,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_548EF2076EF26D7E.mvfrm","enqueuedNodeIndex":103,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_975CFD6F88652DB3.mvfrm","enqueuedNodeIndex":104,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_B845EAF6ABDF1C86.mvfrm","enqueuedNodeIndex":105,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_8DDE3021FF9F8D2B.mvfrm","enqueuedNodeIndex":106,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_8B820BD2AC6A239F.mvfrm","enqueuedNodeIndex":107,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_9A1191F70F63F6BB.mvfrm","enqueuedNodeIndex":108,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_AEAC1BB5544068A9.mvfrm","enqueuedNodeIndex":109,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_AE12D3B1B24D183C.mvfrm","enqueuedNodeIndex":110,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_95038191186148C0.mvfrm","enqueuedNodeIndex":111,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_08577EFB2D8C4EDA.mvfrm","enqueuedNodeIndex":112,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3CCE213C8D987695.mvfrm","enqueuedNodeIndex":113,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_02AC65D6454A1D51.mvfrm","enqueuedNodeIndex":114,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_A7D7A27FD520B061.mvfrm","enqueuedNodeIndex":115,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_E3DEF0019A378312.mvfrm","enqueuedNodeIndex":116,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_4191D5C1BDD09DE5.mvfrm","enqueuedNodeIndex":117,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_5D919F001659B1C7.mvfrm","enqueuedNodeIndex":118,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_A761188D10ACF2F6.mvfrm","enqueuedNodeIndex":119,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_0F47E031DE7CBED2.mvfrm","enqueuedNodeIndex":120,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_DA1FDB9199E74054.mvfrm","enqueuedNodeIndex":121,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_D6C87365A8B0A170.mvfrm","enqueuedNodeIndex":128,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_22DA848A55AE3E42.mvfrm","enqueuedNodeIndex":129,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_1280E01E43015E29.mvfrm","enqueuedNodeIndex":130,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_7F371AF1A9E7F3FC.mvfrm","enqueuedNodeIndex":131,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_28DAF9479429A673.mvfrm","enqueuedNodeIndex":132,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_D01CC5A6D0F5DAF9.mvfrm","enqueuedNodeIndex":133,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_673AEEFA91C338F1.mvfrm","enqueuedNodeIndex":134,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_8189EC8801BEEB18.mvfrm","enqueuedNodeIndex":135,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_E55D0F7C63F01D9E.mvfrm","enqueuedNodeIndex":142,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)","enqueuedNodeIndex":4,"enqueueingNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_E55D0F7C63F01D9E.mvfrm","enqueueingNodeIndex":142}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt","enqueuedNodeIndex":1,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)","enqueueingNodeIndex":4}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp","enqueuedNodeIndex":2,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)","enqueueingNodeIndex":4}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp2","enqueuedNodeIndex":3,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)","enqueueingNodeIndex":4}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm","enqueuedNodeIndex":123,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)","enqueueingNodeIndex":4}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp","enqueuedNodeIndex":122,"enqueueingNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm","enqueueingNodeIndex":123}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_6470942D9C999E52.mvfrm","enqueuedNodeIndex":143,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_5EE0364917308BF5.mvfrm","enqueuedNodeIndex":144,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_193EC4CE382CBFB3.mvfrm","enqueuedNodeIndex":151,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)","enqueuedNodeIndex":141,"enqueueingNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_193EC4CE382CBFB3.mvfrm","enqueueingNodeIndex":151}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt","enqueuedNodeIndex":138,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)","enqueueingNodeIndex":141}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp","enqueuedNodeIndex":139,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)","enqueueingNodeIndex":141}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2","enqueuedNodeIndex":140,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)","enqueueingNodeIndex":141}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm","enqueuedNodeIndex":146,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)","enqueueingNodeIndex":141}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp","enqueuedNodeIndex":145,"enqueueingNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm","enqueueingNodeIndex":146}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_08FEAA520A2EFD60.mvfrm","enqueuedNodeIndex":152,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)","enqueuedNodeIndex":127,"enqueueingNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_08FEAA520A2EFD60.mvfrm","enqueueingNodeIndex":152}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt","enqueuedNodeIndex":124,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)","enqueueingNodeIndex":127}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp","enqueuedNodeIndex":125,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)","enqueueingNodeIndex":127}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2","enqueuedNodeIndex":126,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)","enqueueingNodeIndex":127}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm","enqueuedNodeIndex":137,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)","enqueueingNodeIndex":127}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp","enqueuedNodeIndex":136,"enqueueingNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm","enqueueingNodeIndex":137}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_4C98D3F7040CD4F5.mvfrm","enqueuedNodeIndex":159,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)","enqueuedNodeIndex":150,"enqueueingNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_4C98D3F7040CD4F5.mvfrm","enqueueingNodeIndex":159}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt","enqueuedNodeIndex":147,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)","enqueueingNodeIndex":150}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp","enqueuedNodeIndex":148,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)","enqueueingNodeIndex":150}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp2","enqueuedNodeIndex":149,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)","enqueueingNodeIndex":150}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm","enqueuedNodeIndex":154,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)","enqueueingNodeIndex":150}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp","enqueuedNodeIndex":153,"enqueueingNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm","enqueueingNodeIndex":154}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PlasticSCM.Editor.ref.dll_257AEB342BE77856.mvfrm","enqueuedNodeIndex":232,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)","enqueuedNodeIndex":177,"enqueueingNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PlasticSCM.Editor.ref.dll_257AEB342BE77856.mvfrm","enqueueingNodeIndex":232}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt","enqueuedNodeIndex":174,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)","enqueueingNodeIndex":177}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp","enqueuedNodeIndex":175,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)","enqueueingNodeIndex":177}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp2","enqueuedNodeIndex":176,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)","enqueueingNodeIndex":177}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm","enqueuedNodeIndex":179,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)","enqueueingNodeIndex":177}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp","enqueuedNodeIndex":178,"enqueueingNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm","enqueueingNodeIndex":179}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_EE36537354EA42C8.mvfrm","enqueuedNodeIndex":239,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)","enqueuedNodeIndex":165,"enqueueingNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_EE36537354EA42C8.mvfrm","enqueueingNodeIndex":239}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt","enqueuedNodeIndex":162,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)","enqueueingNodeIndex":165}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp","enqueuedNodeIndex":163,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)","enqueueingNodeIndex":165}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp2","enqueuedNodeIndex":164,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)","enqueueingNodeIndex":165}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm","enqueuedNodeIndex":167,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)","enqueueingNodeIndex":165}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp","enqueuedNodeIndex":166,"enqueueingNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm","enqueueingNodeIndex":167}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Settings.Editor.ref.dll_7F5B154C05A780B0.mvfrm","enqueuedNodeIndex":246,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)","enqueuedNodeIndex":189,"enqueueingNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Settings.Editor.ref.dll_7F5B154C05A780B0.mvfrm","enqueueingNodeIndex":246}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.UnityAdditionalFile.txt","enqueuedNodeIndex":186,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)","enqueueingNodeIndex":189}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp","enqueuedNodeIndex":187,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)","enqueueingNodeIndex":189}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp2","enqueuedNodeIndex":188,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)","enqueueingNodeIndex":189}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm","enqueuedNodeIndex":191,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)","enqueueingNodeIndex":189}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm.rsp","enqueuedNodeIndex":190,"enqueueingNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm","enqueueingNodeIndex":191}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.ref.dll_D3B9364D6FB03093.mvfrm","enqueuedNodeIndex":247,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)","enqueuedNodeIndex":195,"enqueueingNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.ref.dll_D3B9364D6FB03093.mvfrm","enqueueingNodeIndex":247}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.UnityAdditionalFile.txt","enqueuedNodeIndex":192,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)","enqueueingNodeIndex":195}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp","enqueuedNodeIndex":193,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)","enqueueingNodeIndex":195}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp2","enqueuedNodeIndex":194,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)","enqueueingNodeIndex":195}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm","enqueuedNodeIndex":197,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)","enqueueingNodeIndex":195}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm.rsp","enqueuedNodeIndex":196,"enqueueingNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm","enqueueingNodeIndex":197}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.ref.dll_E668CD773429A040.mvfrm","enqueuedNodeIndex":248,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)","enqueuedNodeIndex":201,"enqueueingNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.ref.dll_E668CD773429A040.mvfrm","enqueueingNodeIndex":248}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.UnityAdditionalFile.txt","enqueuedNodeIndex":198,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)","enqueueingNodeIndex":201}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp","enqueuedNodeIndex":199,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)","enqueueingNodeIndex":201}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp2","enqueuedNodeIndex":200,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)","enqueueingNodeIndex":201}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm","enqueuedNodeIndex":203,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)","enqueueingNodeIndex":201}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm.rsp","enqueuedNodeIndex":202,"enqueueingNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm","enqueueingNodeIndex":203}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_84A4293DBDD182DB.mvfrm","enqueuedNodeIndex":255,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)","enqueuedNodeIndex":207,"enqueueingNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_84A4293DBDD182DB.mvfrm","enqueueingNodeIndex":255}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt","enqueuedNodeIndex":204,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)","enqueueingNodeIndex":207}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp","enqueuedNodeIndex":205,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)","enqueueingNodeIndex":207}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp2","enqueuedNodeIndex":206,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)","enqueueingNodeIndex":207}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm","enqueuedNodeIndex":209,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)","enqueueingNodeIndex":207}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp","enqueuedNodeIndex":208,"enqueueingNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm","enqueueingNodeIndex":209}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_621CDDF9C514DF8F.mvfrm","enqueuedNodeIndex":262,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)","enqueuedNodeIndex":213,"enqueueingNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_621CDDF9C514DF8F.mvfrm","enqueueingNodeIndex":262}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt","enqueuedNodeIndex":210,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)","enqueueingNodeIndex":213}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp","enqueuedNodeIndex":211,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)","enqueueingNodeIndex":213}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2","enqueuedNodeIndex":212,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)","enqueueingNodeIndex":213}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm","enqueuedNodeIndex":215,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)","enqueueingNodeIndex":213}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm.rsp","enqueuedNodeIndex":214,"enqueueingNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm","enqueueingNodeIndex":215}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.ref.dll_D75DF4EA1AFE54F4.mvfrm","enqueuedNodeIndex":269,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)","enqueuedNodeIndex":219,"enqueueingNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.ref.dll_D75DF4EA1AFE54F4.mvfrm","enqueueingNodeIndex":269}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt","enqueuedNodeIndex":216,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)","enqueueingNodeIndex":219}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp","enqueuedNodeIndex":217,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)","enqueueingNodeIndex":219}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp2","enqueuedNodeIndex":218,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)","enqueueingNodeIndex":219}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm","enqueuedNodeIndex":221,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)","enqueueingNodeIndex":219}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp","enqueuedNodeIndex":220,"enqueueingNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm","enqueueingNodeIndex":221}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.Editor.ref.dll_EDC8690F57C5BDFD.mvfrm","enqueuedNodeIndex":282,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)","enqueuedNodeIndex":268,"enqueueingNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.Editor.ref.dll_EDC8690F57C5BDFD.mvfrm","enqueueingNodeIndex":282}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt","enqueuedNodeIndex":265,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)","enqueueingNodeIndex":268}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp","enqueuedNodeIndex":266,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)","enqueueingNodeIndex":268}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2","enqueuedNodeIndex":267,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)","enqueueingNodeIndex":268}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm","enqueuedNodeIndex":271,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)","enqueueingNodeIndex":268}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp","enqueuedNodeIndex":270,"enqueueingNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm","enqueueingNodeIndex":271}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.ref.dll_1E6ED5409D07C9A3.mvfrm","enqueuedNodeIndex":283,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)","enqueuedNodeIndex":275,"enqueueingNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.ref.dll_1E6ED5409D07C9A3.mvfrm","enqueueingNodeIndex":283}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt","enqueuedNodeIndex":272,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)","enqueueingNodeIndex":275}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp","enqueuedNodeIndex":273,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)","enqueueingNodeIndex":275}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp2","enqueuedNodeIndex":274,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)","enqueueingNodeIndex":275}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm","enqueuedNodeIndex":277,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)","enqueueingNodeIndex":275}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp","enqueuedNodeIndex":276,"enqueueingNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm","enqueueingNodeIndex":277}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.Editor.ref.dll_6AF951BE66A4493E.mvfrm","enqueuedNodeIndex":296,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)","enqueuedNodeIndex":281,"enqueueingNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.Editor.ref.dll_6AF951BE66A4493E.mvfrm","enqueueingNodeIndex":296}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt","enqueuedNodeIndex":278,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)","enqueueingNodeIndex":281}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp","enqueuedNodeIndex":279,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)","enqueueingNodeIndex":281}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2","enqueuedNodeIndex":280,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)","enqueueingNodeIndex":281}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm","enqueuedNodeIndex":285,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)","enqueueingNodeIndex":281}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp","enqueuedNodeIndex":284,"enqueueingNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm","enqueueingNodeIndex":285}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.ref.dll_D91762B7076BE462.mvfrm","enqueuedNodeIndex":297,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)","enqueuedNodeIndex":289,"enqueueingNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.ref.dll_D91762B7076BE462.mvfrm","enqueueingNodeIndex":297}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt","enqueuedNodeIndex":286,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)","enqueueingNodeIndex":289}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp","enqueuedNodeIndex":287,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)","enqueueingNodeIndex":289}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp2","enqueuedNodeIndex":288,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)","enqueueingNodeIndex":289}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm","enqueuedNodeIndex":291,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)","enqueueingNodeIndex":289}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp","enqueuedNodeIndex":290,"enqueueingNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm","enqueueingNodeIndex":291}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.Editor.ref.dll_E03858E727F23F6F.mvfrm","enqueuedNodeIndex":310,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)","enqueuedNodeIndex":303,"enqueueingNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.Editor.ref.dll_E03858E727F23F6F.mvfrm","enqueueingNodeIndex":310}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt","enqueuedNodeIndex":300,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)","enqueueingNodeIndex":303}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp","enqueuedNodeIndex":301,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)","enqueueingNodeIndex":303}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp2","enqueuedNodeIndex":302,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)","enqueueingNodeIndex":303}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm","enqueuedNodeIndex":305,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)","enqueueingNodeIndex":303}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp","enqueuedNodeIndex":304,"enqueueingNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm","enqueueingNodeIndex":305}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.EditorCoroutines.Editor.ref.dll_34ED10A34098B2DB.mvfrm","enqueuedNodeIndex":317,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)","enqueuedNodeIndex":158,"enqueueingNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.EditorCoroutines.Editor.ref.dll_34ED10A34098B2DB.mvfrm","enqueueingNodeIndex":317}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.UnityAdditionalFile.txt","enqueuedNodeIndex":155,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)","enqueueingNodeIndex":158}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.rsp","enqueuedNodeIndex":156,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)","enqueueingNodeIndex":158}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.rsp2","enqueuedNodeIndex":157,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)","enqueueingNodeIndex":158}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm","enqueuedNodeIndex":161,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)","enqueueingNodeIndex":158}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm.rsp","enqueuedNodeIndex":160,"enqueueingNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm","enqueueingNodeIndex":161}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Editor.ref.dll_EB1F4B6E56116D4A.mvfrm","enqueuedNodeIndex":318,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)","enqueuedNodeIndex":238,"enqueueingNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Editor.ref.dll_EB1F4B6E56116D4A.mvfrm","enqueueingNodeIndex":318}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt","enqueuedNodeIndex":235,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)","enqueueingNodeIndex":238}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp","enqueuedNodeIndex":236,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)","enqueueingNodeIndex":238}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2","enqueuedNodeIndex":237,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)","enqueueingNodeIndex":238}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm","enqueuedNodeIndex":241,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)","enqueueingNodeIndex":238}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp","enqueuedNodeIndex":240,"enqueueingNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm","enqueueingNodeIndex":241}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Performance.Profile-Analyzer.Editor.ref.dll_65A192712782EB2B.mvfrm","enqueuedNodeIndex":319,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)","enqueuedNodeIndex":171,"enqueueingNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Performance.Profile-Analyzer.Editor.ref.dll_65A192712782EB2B.mvfrm","enqueueingNodeIndex":319}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.UnityAdditionalFile.txt","enqueuedNodeIndex":168,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)","enqueueingNodeIndex":171}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp","enqueuedNodeIndex":169,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)","enqueueingNodeIndex":171}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp2","enqueuedNodeIndex":170,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)","enqueueingNodeIndex":171}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm","enqueuedNodeIndex":173,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)","enqueueingNodeIndex":171}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm.rsp","enqueuedNodeIndex":172,"enqueueingNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm","enqueueingNodeIndex":173}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rider.Editor.ref.dll_9B5591808ABA37AF.mvfrm","enqueuedNodeIndex":320,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)","enqueuedNodeIndex":183,"enqueueingNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rider.Editor.ref.dll_9B5591808ABA37AF.mvfrm","enqueueingNodeIndex":320}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt","enqueuedNodeIndex":180,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)","enqueueingNodeIndex":183}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp","enqueuedNodeIndex":181,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)","enqueueingNodeIndex":183}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp2","enqueuedNodeIndex":182,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)","enqueueingNodeIndex":183}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm","enqueuedNodeIndex":185,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)","enqueueingNodeIndex":183}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp","enqueuedNodeIndex":184,"enqueueingNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm","enqueueingNodeIndex":185}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.ref.dll_F09CF4826CE11CD9.mvfrm","enqueuedNodeIndex":321,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)","enqueuedNodeIndex":245,"enqueueingNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.ref.dll_F09CF4826CE11CD9.mvfrm","enqueueingNodeIndex":321}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.UnityAdditionalFile.txt","enqueuedNodeIndex":242,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)","enqueueingNodeIndex":245}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp","enqueuedNodeIndex":243,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)","enqueueingNodeIndex":245}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp2","enqueuedNodeIndex":244,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)","enqueueingNodeIndex":245}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm","enqueuedNodeIndex":250,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)","enqueueingNodeIndex":245}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm.rsp","enqueuedNodeIndex":249,"enqueueingNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm","enqueueingNodeIndex":250}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.Editor.ref.dll_159E061D77A10B86.mvfrm","enqueuedNodeIndex":322,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)","enqueuedNodeIndex":254,"enqueueingNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.Editor.ref.dll_159E061D77A10B86.mvfrm","enqueueingNodeIndex":322}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt","enqueuedNodeIndex":251,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)","enqueueingNodeIndex":254}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp","enqueuedNodeIndex":252,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)","enqueueingNodeIndex":254}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp2","enqueuedNodeIndex":253,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)","enqueueingNodeIndex":254}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm","enqueuedNodeIndex":257,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)","enqueueingNodeIndex":254}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp","enqueuedNodeIndex":256,"enqueueingNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm","enqueueingNodeIndex":257}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.Editor.ref.dll_0D3D3C0B73557612.mvfrm","enqueuedNodeIndex":323,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)","enqueuedNodeIndex":261,"enqueueingNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.Editor.ref.dll_0D3D3C0B73557612.mvfrm","enqueueingNodeIndex":323}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt","enqueuedNodeIndex":258,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)","enqueueingNodeIndex":261}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp","enqueuedNodeIndex":259,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)","enqueueingNodeIndex":261}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2","enqueuedNodeIndex":260,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)","enqueueingNodeIndex":261}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm","enqueuedNodeIndex":264,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)","enqueueingNodeIndex":261}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp","enqueuedNodeIndex":263,"enqueueingNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm","enqueueingNodeIndex":264}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.SettingsProvider.Editor.ref.dll_A1A44354E0B583A8.mvfrm","enqueuedNodeIndex":324,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)","enqueuedNodeIndex":295,"enqueueingNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.SettingsProvider.Editor.ref.dll_A1A44354E0B583A8.mvfrm","enqueueingNodeIndex":324}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt","enqueuedNodeIndex":292,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)","enqueueingNodeIndex":295}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp","enqueuedNodeIndex":293,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)","enqueueingNodeIndex":295}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2","enqueuedNodeIndex":294,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)","enqueueingNodeIndex":295}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm","enqueuedNodeIndex":299,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)","enqueueingNodeIndex":295}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp","enqueuedNodeIndex":298,"enqueueingNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm","enqueueingNodeIndex":299}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Shared.Editor.ref.dll_9669F6EBFF6DFFB7.mvfrm","enqueuedNodeIndex":325,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)","enqueuedNodeIndex":309,"enqueueingNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Shared.Editor.ref.dll_9669F6EBFF6DFFB7.mvfrm","enqueueingNodeIndex":325}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt","enqueuedNodeIndex":306,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)","enqueueingNodeIndex":309}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp","enqueuedNodeIndex":307,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)","enqueueingNodeIndex":309}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2","enqueuedNodeIndex":308,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)","enqueueingNodeIndex":309}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm","enqueuedNodeIndex":312,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)","enqueueingNodeIndex":309}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp","enqueuedNodeIndex":311,"enqueueingNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm","enqueueingNodeIndex":312}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualStudio.Editor.ref.dll_3A975DBA53ABA4AD.mvfrm","enqueuedNodeIndex":326,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)","enqueuedNodeIndex":225,"enqueueingNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualStudio.Editor.ref.dll_3A975DBA53ABA4AD.mvfrm","enqueueingNodeIndex":326}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt","enqueuedNodeIndex":222,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)","enqueueingNodeIndex":225}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp","enqueuedNodeIndex":223,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)","enqueueingNodeIndex":225}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp2","enqueuedNodeIndex":224,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)","enqueueingNodeIndex":225}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm","enqueuedNodeIndex":227,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)","enqueueingNodeIndex":225}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp","enqueuedNodeIndex":226,"enqueueingNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm","enqueueingNodeIndex":227}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.dll","enqueuedNodeIndex":329,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.pdb","enqueuedNodeIndex":330,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.dll","enqueuedNodeIndex":331,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.pdb","enqueuedNodeIndex":332,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.dll","enqueuedNodeIndex":333,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.pdb","enqueuedNodeIndex":334,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll","enqueuedNodeIndex":335,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb","enqueuedNodeIndex":336,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/UnityEngine.UI.dll","enqueuedNodeIndex":337,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/UnityEngine.UI.pdb","enqueuedNodeIndex":338,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/UnityEditor.UI.dll","enqueuedNodeIndex":339,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/UnityEditor.UI.pdb","enqueuedNodeIndex":340,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.dll","enqueuedNodeIndex":341,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.pdb","enqueuedNodeIndex":342,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.dll","enqueuedNodeIndex":343,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb","enqueuedNodeIndex":344,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.dll","enqueuedNodeIndex":345,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.pdb","enqueuedNodeIndex":346,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.dll","enqueuedNodeIndex":347,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.pdb","enqueuedNodeIndex":348,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.dll","enqueuedNodeIndex":349,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.pdb","enqueuedNodeIndex":350,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll","enqueuedNodeIndex":351,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.pdb","enqueuedNodeIndex":352,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll","enqueuedNodeIndex":353,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.pdb","enqueuedNodeIndex":354,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.dll","enqueuedNodeIndex":355,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb","enqueuedNodeIndex":356,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.Timeline.dll","enqueuedNodeIndex":357,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.Timeline.pdb","enqueuedNodeIndex":358,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.dll","enqueuedNodeIndex":359,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.pdb","enqueuedNodeIndex":360,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.dll","enqueuedNodeIndex":361,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)","enqueuedNodeIndex":231,"enqueueingNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.dll","enqueueingNodeIndex":361}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt","enqueuedNodeIndex":228,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)","enqueueingNodeIndex":231}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp","enqueuedNodeIndex":229,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)","enqueueingNodeIndex":231}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp2","enqueuedNodeIndex":230,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)","enqueueingNodeIndex":231}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm","enqueuedNodeIndex":234,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)","enqueueingNodeIndex":231}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp","enqueuedNodeIndex":233,"enqueueingNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm","enqueueingNodeIndex":234}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.pdb","enqueuedNodeIndex":362,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.dll","enqueuedNodeIndex":363,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.pdb","enqueuedNodeIndex":364,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.dll","enqueuedNodeIndex":365,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.pdb","enqueuedNodeIndex":366,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll","enqueuedNodeIndex":367,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb","enqueuedNodeIndex":368,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.dll","enqueuedNodeIndex":369,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.pdb","enqueuedNodeIndex":370,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll","enqueuedNodeIndex":371,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.pdb","enqueuedNodeIndex":372,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.dll","enqueuedNodeIndex":373,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.pdb","enqueuedNodeIndex":374,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.dll","enqueuedNodeIndex":375,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.pdb","enqueuedNodeIndex":376,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.dll","enqueuedNodeIndex":377,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.pdb","enqueuedNodeIndex":378,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll","enqueuedNodeIndex":379,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.pdb","enqueuedNodeIndex":380,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.dll","enqueuedNodeIndex":381,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.pdb","enqueuedNodeIndex":382,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.dll","enqueuedNodeIndex":383,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.pdb","enqueuedNodeIndex":384,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll","enqueuedNodeIndex":385,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)","enqueuedNodeIndex":316,"enqueueingNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll","enqueueingNodeIndex":385}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt","enqueuedNodeIndex":313,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)","enqueueingNodeIndex":316}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp","enqueuedNodeIndex":314,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)","enqueueingNodeIndex":316}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp2","enqueuedNodeIndex":315,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)","enqueueingNodeIndex":316}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm","enqueuedNodeIndex":328,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)","enqueueingNodeIndex":316}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp","enqueuedNodeIndex":327,"enqueueingNodeAnnotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm","enqueueingNodeIndex":328}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb","enqueuedNodeIndex":386,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_06CF1E61B9937147.mvfrm","index":15}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_8E8F8EE39FADED60.mvfrm","index":19}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_7B346C974D1F5AEB.mvfrm","index":17}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_11DB9A34FA868FF7.mvfrm","index":13}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_B2E90B5842DD1C21.mvfrm","index":16}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_986F749CC4F33028.mvfrm","index":18}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_E605164F31FC9E53.mvfrm","index":14}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_06CF1E61B9937147.mvfrm","displayName":"Checking for moved APIs","index":15}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_8E8F8EE39FADED60.mvfrm","displayName":"Checking for moved APIs","index":19}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_53494DA4BD956B35.mvfrm","index":20}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_53494DA4BD956B35.mvfrm","displayName":"Checking for moved APIs","index":20}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_7B346C974D1F5AEB.mvfrm","displayName":"Checking for moved APIs","index":17}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_11DB9A34FA868FF7.mvfrm","displayName":"Checking for moved APIs","index":13}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_B2E90B5842DD1C21.mvfrm","displayName":"Checking for moved APIs","index":16}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_986F749CC4F33028.mvfrm","displayName":"Checking for moved APIs","index":18}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_E605164F31FC9E53.mvfrm","displayName":"Checking for moved APIs","index":14}
{"msg":"noderesult","processed_node_count":0,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_8E8F8EE39FADED60.mvfrm","index":19,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.GridAndSnapModule.dll_8E8F8EE39FADED60.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_CDB1A95E7E145EAF.mvfrm","index":21}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_CDB1A95E7E145EAF.mvfrm","displayName":"Checking for moved APIs","index":21}
{"msg":"noderesult","processed_node_count":1,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_E605164F31FC9E53.mvfrm","index":14,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.EditorToolbarModule.dll_E605164F31FC9E53.mvfrm"}
{"msg":"noderesult","processed_node_count":1,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_53494DA4BD956B35.mvfrm","index":20,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.GridModule.dll_53494DA4BD956B35.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_FDB4EFCD5ACD4A7E.mvfrm","index":22}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_FDB4EFCD5ACD4A7E.mvfrm","displayName":"Checking for moved APIs","index":22}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_7127B1C813DA0DCC.mvfrm","index":23}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_7127B1C813DA0DCC.mvfrm","displayName":"Checking for moved APIs","index":23}
{"msg":"noderesult","processed_node_count":3,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_986F749CC4F33028.mvfrm","index":18,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.GraphViewModule.dll_986F749CC4F33028.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_53D5AD7EC74B882D.mvfrm","index":24}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_53D5AD7EC74B882D.mvfrm","displayName":"Checking for moved APIs","index":24}
{"msg":"noderesult","processed_node_count":4,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_06CF1E61B9937147.mvfrm","index":15,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.EmbreeModule.dll_06CF1E61B9937147.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_1CB45D079AA45272.mvfrm","index":25}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_1CB45D079AA45272.mvfrm","displayName":"Checking for moved APIs","index":25}
{"msg":"noderesult","processed_node_count":5,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_7B346C974D1F5AEB.mvfrm","index":17,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.GraphicsStateCollectionSerializerModule.dll_7B346C974D1F5AEB.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_0FE6296F75202CCA.mvfrm","index":26}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_0FE6296F75202CCA.mvfrm","displayName":"Checking for moved APIs","index":26}
{"msg":"noderesult","processed_node_count":6,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_B2E90B5842DD1C21.mvfrm","index":16,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.GIModule.dll_B2E90B5842DD1C21.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_34FDE67FD72C093F.mvfrm","index":27}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_34FDE67FD72C093F.mvfrm","displayName":"Checking for moved APIs","index":27}
{"msg":"noderesult","processed_node_count":7,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_11DB9A34FA868FF7.mvfrm","index":13,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.dll_11DB9A34FA868FF7.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_09A4870D30E9488B.mvfrm","index":28}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_09A4870D30E9488B.mvfrm","displayName":"Checking for moved APIs","index":28}
{"msg":"noderesult","processed_node_count":8,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_7127B1C813DA0DCC.mvfrm","index":23,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.PhysicsModule.dll_7127B1C813DA0DCC.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_D71F57A4BA15FD36.mvfrm","index":29}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_D71F57A4BA15FD36.mvfrm","displayName":"Checking for moved APIs","index":29}
{"msg":"noderesult","processed_node_count":9,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_53D5AD7EC74B882D.mvfrm","index":24,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.PresetsUIModule.dll_53D5AD7EC74B882D.mvfrm"}
{"msg":"noderesult","processed_node_count":10,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_34FDE67FD72C093F.mvfrm","index":27,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.SafeModeModule.dll_34FDE67FD72C093F.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_1D12CBD0225DA556.mvfrm","index":31}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_1D12CBD0225DA556.mvfrm","displayName":"Checking for moved APIs","index":31}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_488457505398C519.mvfrm","index":30}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_488457505398C519.mvfrm","displayName":"Checking for moved APIs","index":30}
{"msg":"noderesult","processed_node_count":11,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_1CB45D079AA45272.mvfrm","index":25,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.PropertiesModule.dll_1CB45D079AA45272.mvfrm"}
{"msg":"noderesult","processed_node_count":11,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_FDB4EFCD5ACD4A7E.mvfrm","index":22,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.Physics2DModule.dll_FDB4EFCD5ACD4A7E.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_D8A59059492883B0.mvfrm","index":32}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_D8A59059492883B0.mvfrm","displayName":"Checking for moved APIs","index":32}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_676ABA2C7AEF0941.mvfrm","index":33}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_676ABA2C7AEF0941.mvfrm","displayName":"Checking for moved APIs","index":33}
{"msg":"noderesult","processed_node_count":13,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_CDB1A95E7E145EAF.mvfrm","index":21,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.MultiplayerModule.dll_CDB1A95E7E145EAF.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_424F017654AB0835.mvfrm","index":34}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_424F017654AB0835.mvfrm","displayName":"Checking for moved APIs","index":34}
{"msg":"noderesult","processed_node_count":14,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_0FE6296F75202CCA.mvfrm","index":26,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.QuickSearchModule.dll_0FE6296F75202CCA.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_8B6E2D36E99856BA.mvfrm","index":35}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_8B6E2D36E99856BA.mvfrm","displayName":"Checking for moved APIs","index":35}
{"msg":"noderesult","processed_node_count":15,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_09A4870D30E9488B.mvfrm","index":28,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.SceneTemplateModule.dll_09A4870D30E9488B.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_B98AA527428CA21A.mvfrm","index":37}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_B98AA527428CA21A.mvfrm","displayName":"Checking for moved APIs","index":37}
{"msg":"noderesult","processed_node_count":16,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_D71F57A4BA15FD36.mvfrm","index":29,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.SceneViewModule.dll_D71F57A4BA15FD36.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_5347A9488345F9E4.mvfrm","index":38}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_5347A9488345F9E4.mvfrm","displayName":"Checking for moved APIs","index":38}
{"msg":"noderesult","processed_node_count":17,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_D8A59059492883B0.mvfrm","index":32,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.SpriteMaskModule.dll_D8A59059492883B0.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_2A6A94B8015537C3.mvfrm","index":39}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_2A6A94B8015537C3.mvfrm","displayName":"Checking for moved APIs","index":39}
{"msg":"noderesult","processed_node_count":18,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_488457505398C519.mvfrm","index":30,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.ShaderFoundryModule.dll_488457505398C519.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_D38426C52D066A7D.mvfrm","index":40}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_D38426C52D066A7D.mvfrm","displayName":"Checking for moved APIs","index":40}
{"msg":"noderesult","processed_node_count":19,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_B98AA527428CA21A.mvfrm","index":37,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.TextCoreTextEngineModule.dll_B98AA527428CA21A.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_5389693E2972069A.mvfrm","index":41}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_5389693E2972069A.mvfrm","displayName":"Checking for moved APIs","index":41}
{"msg":"noderesult","processed_node_count":20,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_424F017654AB0835.mvfrm","index":34,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.SubstanceModule.dll_424F017654AB0835.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_ECF821B937ABA95C.mvfrm","index":42}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_ECF821B937ABA95C.mvfrm","displayName":"Checking for moved APIs","index":42}
{"msg":"noderesult","processed_node_count":21,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_1D12CBD0225DA556.mvfrm","index":31,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.SketchUpModule.dll_1D12CBD0225DA556.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_479158231A9CA7BA.mvfrm","index":43}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_479158231A9CA7BA.mvfrm","displayName":"Checking for moved APIs","index":43}
{"msg":"noderesult","processed_node_count":22,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_676ABA2C7AEF0941.mvfrm","index":33,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.SpriteShapeModule.dll_676ABA2C7AEF0941.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_71A26EB5B308E8FC.mvfrm","index":36}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_71A26EB5B308E8FC.mvfrm","displayName":"Checking for moved APIs","index":36}
{"msg":"noderesult","processed_node_count":23,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_8B6E2D36E99856BA.mvfrm","index":35,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.TerrainModule.dll_8B6E2D36E99856BA.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_F2E965C2FA451353.mvfrm","index":44}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_F2E965C2FA451353.mvfrm","displayName":"Checking for moved APIs","index":44}
{"msg":"noderesult","processed_node_count":24,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_5347A9488345F9E4.mvfrm","index":38,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.TextRenderingModule.dll_5347A9488345F9E4.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_D130410B56E7FD0E.mvfrm","index":45}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_D130410B56E7FD0E.mvfrm","displayName":"Checking for moved APIs","index":45}
{"msg":"noderesult","processed_node_count":25,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_2A6A94B8015537C3.mvfrm","index":39,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.TilemapModule.dll_2A6A94B8015537C3.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_9E2287C3B1E222F4.mvfrm","index":46}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_9E2287C3B1E222F4.mvfrm","displayName":"Checking for moved APIs","index":46}
{"msg":"noderesult","processed_node_count":26,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_5389693E2972069A.mvfrm","index":41,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.UIAutomationModule.dll_5389693E2972069A.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_D7BD4DAE2A87750D.mvfrm","index":47}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_D7BD4DAE2A87750D.mvfrm","displayName":"Checking for moved APIs","index":47}
{"msg":"noderesult","processed_node_count":27,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_D38426C52D066A7D.mvfrm","index":40,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.TreeModule.dll_D38426C52D066A7D.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_7B65195D8C536D40.mvfrm","index":48}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_7B65195D8C536D40.mvfrm","displayName":"Checking for moved APIs","index":48}
{"msg":"noderesult","processed_node_count":28,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_ECF821B937ABA95C.mvfrm","index":42,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.UIBuilderModule.dll_ECF821B937ABA95C.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_9BB14A022E6A0C89.mvfrm","index":49}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_9BB14A022E6A0C89.mvfrm","displayName":"Checking for moved APIs","index":49}
{"msg":"noderesult","processed_node_count":29,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_479158231A9CA7BA.mvfrm","index":43,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.UIElementsModule.dll_479158231A9CA7BA.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_3C49F2A4315F7028.mvfrm","index":50}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_3C49F2A4315F7028.mvfrm","displayName":"Checking for moved APIs","index":50}
{"msg":"noderesult","processed_node_count":30,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_71A26EB5B308E8FC.mvfrm","index":36,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.TextCoreFontEngineModule.dll_71A26EB5B308E8FC.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_591B53DA12AB67D2.mvfrm","index":51}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_591B53DA12AB67D2.mvfrm","displayName":"Checking for moved APIs","index":51}
{"msg":"noderesult","processed_node_count":31,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_F2E965C2FA451353.mvfrm","index":44,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.UIElementsSamplesModule.dll_F2E965C2FA451353.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_C5DA40F663BC1690.mvfrm","index":52}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_C5DA40F663BC1690.mvfrm","displayName":"Checking for moved APIs","index":52}
{"msg":"noderesult","processed_node_count":32,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_D130410B56E7FD0E.mvfrm","index":45,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.UmbraModule.dll_D130410B56E7FD0E.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_66F4F0C59F5004CE.mvfrm","index":53}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_66F4F0C59F5004CE.mvfrm","displayName":"Checking for moved APIs","index":53}
{"msg":"noderesult","processed_node_count":33,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_D7BD4DAE2A87750D.mvfrm","index":47,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.VFXModule.dll_D7BD4DAE2A87750D.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_128E18DD6FC8C69B.mvfrm","index":54}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_128E18DD6FC8C69B.mvfrm","displayName":"Checking for moved APIs","index":54}
{"msg":"noderesult","processed_node_count":34,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_9E2287C3B1E222F4.mvfrm","index":46,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.UnityConnectModule.dll_9E2287C3B1E222F4.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_53CF5C60D786E1BC.mvfrm","index":55}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_53CF5C60D786E1BC.mvfrm","displayName":"Checking for moved APIs","index":55}
{"msg":"noderesult","processed_node_count":35,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_9BB14A022E6A0C89.mvfrm","index":49,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.XRModule.dll_9BB14A022E6A0C89.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_1C885156FB3E8240.mvfrm","index":56}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_1C885156FB3E8240.mvfrm","displayName":"Checking for moved APIs","index":56}
{"msg":"noderesult","processed_node_count":36,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_3C49F2A4315F7028.mvfrm","index":50,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.AccessibilityModule.dll_3C49F2A4315F7028.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_8370AE51D55B6124.mvfrm","index":57}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_8370AE51D55B6124.mvfrm","displayName":"Checking for moved APIs","index":57}
{"msg":"noderesult","processed_node_count":37,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_591B53DA12AB67D2.mvfrm","index":51,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.AIModule.dll_591B53DA12AB67D2.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_1DBEFCD96369F5BA.mvfrm","index":58}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_1DBEFCD96369F5BA.mvfrm","displayName":"Checking for moved APIs","index":58}
{"msg":"noderesult","processed_node_count":38,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_7B65195D8C536D40.mvfrm","index":48,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.VideoModule.dll_7B65195D8C536D40.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_8AC679A0A072FC67.mvfrm","index":59}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_8AC679A0A072FC67.mvfrm","displayName":"Checking for moved APIs","index":59}
{"msg":"noderesult","processed_node_count":39,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_C5DA40F663BC1690.mvfrm","index":52,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.AndroidJNIModule.dll_C5DA40F663BC1690.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_ECE5B5A1A60989A9.mvfrm","index":60}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_ECE5B5A1A60989A9.mvfrm","displayName":"Checking for moved APIs","index":60}
{"msg":"noderesult","processed_node_count":40,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_66F4F0C59F5004CE.mvfrm","index":53,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.AnimationModule.dll_66F4F0C59F5004CE.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_98F905094D464069.mvfrm","index":61}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_98F905094D464069.mvfrm","displayName":"Checking for moved APIs","index":61}
{"msg":"noderesult","processed_node_count":41,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_53CF5C60D786E1BC.mvfrm","index":55,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.AssetBundleModule.dll_53CF5C60D786E1BC.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_1B2C760BE566AFA1.mvfrm","index":62}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_1B2C760BE566AFA1.mvfrm","displayName":"Checking for moved APIs","index":62}
{"msg":"noderesult","processed_node_count":42,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_128E18DD6FC8C69B.mvfrm","index":54,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.ARModule.dll_128E18DD6FC8C69B.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_3014A9005AF6E0C6.mvfrm","index":63}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_3014A9005AF6E0C6.mvfrm","displayName":"Checking for moved APIs","index":63}
{"msg":"noderesult","processed_node_count":43,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_1C885156FB3E8240.mvfrm","index":56,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.AudioModule.dll_1C885156FB3E8240.mvfrm"}
{"msg":"noderesult","processed_node_count":44,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_8370AE51D55B6124.mvfrm","index":57,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.ClothModule.dll_8370AE51D55B6124.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_447FE10D58FA1CDC.mvfrm","index":64}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_447FE10D58FA1CDC.mvfrm","displayName":"Checking for moved APIs","index":64}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_35660A69CD57B1E7.mvfrm","index":65}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_35660A69CD57B1E7.mvfrm","displayName":"Checking for moved APIs","index":65}
{"msg":"noderesult","processed_node_count":45,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_1DBEFCD96369F5BA.mvfrm","index":58,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.ClusterInputModule.dll_1DBEFCD96369F5BA.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_F0278B1129FD0781.mvfrm","index":66}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_F0278B1129FD0781.mvfrm","displayName":"Checking for moved APIs","index":66}
{"msg":"noderesult","processed_node_count":46,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_8AC679A0A072FC67.mvfrm","index":59,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.ClusterRendererModule.dll_8AC679A0A072FC67.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_F1579CE7AE5A4FEC.mvfrm","index":67}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_F1579CE7AE5A4FEC.mvfrm","displayName":"Checking for moved APIs","index":67}
{"msg":"noderesult","processed_node_count":47,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_ECE5B5A1A60989A9.mvfrm","index":60,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.ContentLoadModule.dll_ECE5B5A1A60989A9.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_842839871A1EA450.mvfrm","index":68}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_842839871A1EA450.mvfrm","displayName":"Checking for moved APIs","index":68}
{"msg":"noderesult","processed_node_count":48,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_98F905094D464069.mvfrm","index":61,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.CoreModule.dll_98F905094D464069.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_766A20F7659AD660.mvfrm","index":69}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_766A20F7659AD660.mvfrm","displayName":"Checking for moved APIs","index":69}
{"msg":"noderesult","processed_node_count":49,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_F0278B1129FD0781.mvfrm","index":66,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.GameCenterModule.dll_F0278B1129FD0781.mvfrm"}
{"msg":"noderesult","processed_node_count":49,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_3014A9005AF6E0C6.mvfrm","index":63,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.DirectorModule.dll_3014A9005AF6E0C6.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_E83DAECB7A832050.mvfrm","index":71}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_E83DAECB7A832050.mvfrm","displayName":"Checking for moved APIs","index":71}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_B3648F7EA00CC91E.mvfrm","index":70}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_B3648F7EA00CC91E.mvfrm","displayName":"Checking for moved APIs","index":70}
{"msg":"noderesult","processed_node_count":51,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_35660A69CD57B1E7.mvfrm","index":65,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.DSPGraphModule.dll_35660A69CD57B1E7.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_F6B877512297B45B.mvfrm","index":72}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_F6B877512297B45B.mvfrm","displayName":"Checking for moved APIs","index":72}
{"msg":"noderesult","processed_node_count":52,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_1B2C760BE566AFA1.mvfrm","index":62,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.CrashReportingModule.dll_1B2C760BE566AFA1.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_54E7F0BDC1701621.mvfrm","index":73}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_54E7F0BDC1701621.mvfrm","displayName":"Checking for moved APIs","index":73}
{"msg":"noderesult","processed_node_count":53,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_447FE10D58FA1CDC.mvfrm","index":64,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.dll_447FE10D58FA1CDC.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_F2D9E52A8A1DA43D.mvfrm","index":74}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_F2D9E52A8A1DA43D.mvfrm","displayName":"Checking for moved APIs","index":74}
{"msg":"noderesult","processed_node_count":54,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_F6B877512297B45B.mvfrm","index":72,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.ImageConversionModule.dll_F6B877512297B45B.mvfrm"}
{"msg":"noderesult","processed_node_count":54,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_F1579CE7AE5A4FEC.mvfrm","index":67,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.GIModule.dll_F1579CE7AE5A4FEC.mvfrm"}
{"msg":"noderesult","processed_node_count":56,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_54E7F0BDC1701621.mvfrm","index":73,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.IMGUIModule.dll_54E7F0BDC1701621.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_82FE19077176A6E4.mvfrm","index":76}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_E514777F6F7897AD.mvfrm","index":75}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_82FE19077176A6E4.mvfrm","displayName":"Checking for moved APIs","index":76}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_9C617BE0309AD0B2.mvfrm","index":77}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_E514777F6F7897AD.mvfrm","displayName":"Checking for moved APIs","index":75}
{"msg":"noderesult","processed_node_count":57,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_B3648F7EA00CC91E.mvfrm","index":70,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.HierarchyCoreModule.dll_B3648F7EA00CC91E.mvfrm"}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_9C617BE0309AD0B2.mvfrm","displayName":"Checking for moved APIs","index":77}
{"msg":"noderesult","processed_node_count":57,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_842839871A1EA450.mvfrm","index":68,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.GraphicsStateCollectionSerializerModule.dll_842839871A1EA450.mvfrm"}
{"msg":"noderesult","processed_node_count":57,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_E83DAECB7A832050.mvfrm","index":71,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.HotReloadModule.dll_E83DAECB7A832050.mvfrm"}
{"msg":"noderesult","processed_node_count":57,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_766A20F7659AD660.mvfrm","index":69,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.GridModule.dll_766A20F7659AD660.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_5C0BC72BFAE5E8B3.mvfrm","index":78}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_5C0BC72BFAE5E8B3.mvfrm","displayName":"Checking for moved APIs","index":78}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_D37778ED904E6F34.mvfrm","index":80}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_D37778ED904E6F34.mvfrm","displayName":"Checking for moved APIs","index":80}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_FC37FAF5BBAF0F0E.mvfrm","index":79}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_FC37FAF5BBAF0F0E.mvfrm","displayName":"Checking for moved APIs","index":79}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_6E3EBF5853783B2F.mvfrm","index":81}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_6E3EBF5853783B2F.mvfrm","displayName":"Checking for moved APIs","index":81}
{"msg":"noderesult","processed_node_count":61,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_F2D9E52A8A1DA43D.mvfrm","index":74,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.InputForUIModule.dll_F2D9E52A8A1DA43D.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_1D072DB291F67E28.mvfrm","index":82}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_1D072DB291F67E28.mvfrm","displayName":"Checking for moved APIs","index":82}
{"msg":"noderesult","processed_node_count":62,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_82FE19077176A6E4.mvfrm","index":76,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.InputModule.dll_82FE19077176A6E4.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_B39AE0B6404CFC63.mvfrm","index":83}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_B39AE0B6404CFC63.mvfrm","displayName":"Checking for moved APIs","index":83}
{"msg":"noderesult","processed_node_count":63,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_5C0BC72BFAE5E8B3.mvfrm","index":78,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.LocalizationModule.dll_5C0BC72BFAE5E8B3.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_63A26A7278E8F511.mvfrm","index":84}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_63A26A7278E8F511.mvfrm","displayName":"Checking for moved APIs","index":84}
{"msg":"noderesult","processed_node_count":64,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_D37778ED904E6F34.mvfrm","index":80,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.MultiplayerModule.dll_D37778ED904E6F34.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_1D62CA4F9CE86BBD.mvfrm","index":85}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_1D62CA4F9CE86BBD.mvfrm","displayName":"Checking for moved APIs","index":85}
{"msg":"noderesult","processed_node_count":65,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_FC37FAF5BBAF0F0E.mvfrm","index":79,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.MarshallingModule.dll_FC37FAF5BBAF0F0E.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_BF7F8C87F103B32C.mvfrm","index":86}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_BF7F8C87F103B32C.mvfrm","displayName":"Checking for moved APIs","index":86}
{"msg":"noderesult","processed_node_count":66,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_E514777F6F7897AD.mvfrm","index":75,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.InputLegacyModule.dll_E514777F6F7897AD.mvfrm"}
{"msg":"noderesult","processed_node_count":66,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_6E3EBF5853783B2F.mvfrm","index":81,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.ParticleSystemModule.dll_6E3EBF5853783B2F.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_B64929654506FD14.mvfrm","index":87}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_B64929654506FD14.mvfrm","displayName":"Checking for moved APIs","index":87}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_FD883EED3964B210.mvfrm","index":88}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_FD883EED3964B210.mvfrm","displayName":"Checking for moved APIs","index":88}
{"msg":"noderesult","processed_node_count":68,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_9C617BE0309AD0B2.mvfrm","index":77,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.JSONSerializeModule.dll_9C617BE0309AD0B2.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_7AC9B62699C5AFAB.mvfrm","index":89}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_7AC9B62699C5AFAB.mvfrm","displayName":"Checking for moved APIs","index":89}
{"msg":"noderesult","processed_node_count":69,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_1D62CA4F9CE86BBD.mvfrm","index":85,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.PropertiesModule.dll_1D62CA4F9CE86BBD.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_D953FDA14B6C9CFB.mvfrm","index":90}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_D953FDA14B6C9CFB.mvfrm","displayName":"Checking for moved APIs","index":90}
{"msg":"noderesult","processed_node_count":70,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_63A26A7278E8F511.mvfrm","index":84,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.PhysicsModule.dll_63A26A7278E8F511.mvfrm"}
{"msg":"noderesult","processed_node_count":70,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_1D072DB291F67E28.mvfrm","index":82,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.PerformanceReportingModule.dll_1D072DB291F67E28.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_1B89DE15EED7BC0C.mvfrm","index":92}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_1B89DE15EED7BC0C.mvfrm","displayName":"Checking for moved APIs","index":92}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_6D3089BB8D2919C6.mvfrm","index":91}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_6D3089BB8D2919C6.mvfrm","displayName":"Checking for moved APIs","index":91}
{"msg":"noderesult","processed_node_count":72,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_B39AE0B6404CFC63.mvfrm","index":83,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.Physics2DModule.dll_B39AE0B6404CFC63.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_F834F25F3A2ABA1A.mvfrm","index":93}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_F834F25F3A2ABA1A.mvfrm","displayName":"Checking for moved APIs","index":93}
{"msg":"noderesult","processed_node_count":73,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_FD883EED3964B210.mvfrm","index":88,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.ShaderVariantAnalyticsModule.dll_FD883EED3964B210.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_4401640319B4850E.mvfrm","index":94}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_4401640319B4850E.mvfrm","displayName":"Checking for moved APIs","index":94}
{"msg":"noderesult","processed_node_count":74,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_B64929654506FD14.mvfrm","index":87,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.ScreenCaptureModule.dll_B64929654506FD14.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_7DE8E5E14EA73DFF.mvfrm","index":95}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_7DE8E5E14EA73DFF.mvfrm","displayName":"Checking for moved APIs","index":95}
{"msg":"noderesult","processed_node_count":75,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_BF7F8C87F103B32C.mvfrm","index":86,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_BF7F8C87F103B32C.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_6EB9682B6CBB35BC.mvfrm","index":96}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_6EB9682B6CBB35BC.mvfrm","displayName":"Checking for moved APIs","index":96}
{"msg":"noderesult","processed_node_count":76,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_7AC9B62699C5AFAB.mvfrm","index":89,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.SharedInternalsModule.dll_7AC9B62699C5AFAB.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_37EA207A558A60E5.mvfrm","index":98}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_37EA207A558A60E5.mvfrm","displayName":"Checking for moved APIs","index":98}
{"msg":"noderesult","processed_node_count":77,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_6D3089BB8D2919C6.mvfrm","index":91,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.SpriteShapeModule.dll_6D3089BB8D2919C6.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_DF889B6FFC3E7D09.mvfrm","index":99}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_DF889B6FFC3E7D09.mvfrm","displayName":"Checking for moved APIs","index":99}
{"msg":"noderesult","processed_node_count":78,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_D953FDA14B6C9CFB.mvfrm","index":90,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.SpriteMaskModule.dll_D953FDA14B6C9CFB.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_1CE54D6266641F08.mvfrm","index":100}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_1CE54D6266641F08.mvfrm","displayName":"Checking for moved APIs","index":100}
{"msg":"noderesult","processed_node_count":79,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_F834F25F3A2ABA1A.mvfrm","index":93,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.SubstanceModule.dll_F834F25F3A2ABA1A.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_63005792C3CA19A5.mvfrm","index":101}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_63005792C3CA19A5.mvfrm","displayName":"Checking for moved APIs","index":101}
{"msg":"noderesult","processed_node_count":80,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_1B89DE15EED7BC0C.mvfrm","index":92,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.StreamingModule.dll_1B89DE15EED7BC0C.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_483FC56B1CE0C105.mvfrm","index":102}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_483FC56B1CE0C105.mvfrm","displayName":"Checking for moved APIs","index":102}
{"msg":"noderesult","processed_node_count":81,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_4401640319B4850E.mvfrm","index":94,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.SubsystemsModule.dll_4401640319B4850E.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_548EF2076EF26D7E.mvfrm","index":103}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_548EF2076EF26D7E.mvfrm","displayName":"Checking for moved APIs","index":103}
{"msg":"noderesult","processed_node_count":82,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_6EB9682B6CBB35BC.mvfrm","index":96,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.TerrainPhysicsModule.dll_6EB9682B6CBB35BC.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_F001EA08C606A7C7.mvfrm","index":97}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_F001EA08C606A7C7.mvfrm","displayName":"Checking for moved APIs","index":97}
{"msg":"noderesult","processed_node_count":83,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_37EA207A558A60E5.mvfrm","index":98,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.TextCoreTextEngineModule.dll_37EA207A558A60E5.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_975CFD6F88652DB3.mvfrm","index":104}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_975CFD6F88652DB3.mvfrm","displayName":"Checking for moved APIs","index":104}
{"msg":"noderesult","processed_node_count":84,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_7DE8E5E14EA73DFF.mvfrm","index":95,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.TerrainModule.dll_7DE8E5E14EA73DFF.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_B845EAF6ABDF1C86.mvfrm","index":105}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_B845EAF6ABDF1C86.mvfrm","displayName":"Checking for moved APIs","index":105}
{"msg":"noderesult","processed_node_count":85,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_63005792C3CA19A5.mvfrm","index":101,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.TLSModule.dll_63005792C3CA19A5.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_8DDE3021FF9F8D2B.mvfrm","index":106}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_8DDE3021FF9F8D2B.mvfrm","displayName":"Checking for moved APIs","index":106}
{"msg":"noderesult","processed_node_count":86,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_DF889B6FFC3E7D09.mvfrm","index":99,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.TextRenderingModule.dll_DF889B6FFC3E7D09.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_8B820BD2AC6A239F.mvfrm","index":107}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_8B820BD2AC6A239F.mvfrm","displayName":"Checking for moved APIs","index":107}
{"msg":"noderesult","processed_node_count":87,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_1CE54D6266641F08.mvfrm","index":100,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.TilemapModule.dll_1CE54D6266641F08.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_9A1191F70F63F6BB.mvfrm","index":108}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_9A1191F70F63F6BB.mvfrm","displayName":"Checking for moved APIs","index":108}
{"msg":"noderesult","processed_node_count":88,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_483FC56B1CE0C105.mvfrm","index":102,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.UIElementsModule.dll_483FC56B1CE0C105.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_AEAC1BB5544068A9.mvfrm","index":109}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_AEAC1BB5544068A9.mvfrm","displayName":"Checking for moved APIs","index":109}
{"msg":"noderesult","processed_node_count":89,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_F001EA08C606A7C7.mvfrm","index":97,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.TextCoreFontEngineModule.dll_F001EA08C606A7C7.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_AE12D3B1B24D183C.mvfrm","index":110}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_AE12D3B1B24D183C.mvfrm","displayName":"Checking for moved APIs","index":110}
{"msg":"noderesult","processed_node_count":90,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_548EF2076EF26D7E.mvfrm","index":103,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.UIModule.dll_548EF2076EF26D7E.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_95038191186148C0.mvfrm","index":111}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_95038191186148C0.mvfrm","displayName":"Checking for moved APIs","index":111}
{"msg":"noderesult","processed_node_count":91,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_975CFD6F88652DB3.mvfrm","index":104,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.UmbraModule.dll_975CFD6F88652DB3.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_08577EFB2D8C4EDA.mvfrm","index":112}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_08577EFB2D8C4EDA.mvfrm","displayName":"Checking for moved APIs","index":112}
{"msg":"noderesult","processed_node_count":92,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_B845EAF6ABDF1C86.mvfrm","index":105,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.UnityAnalyticsCommonModule.dll_B845EAF6ABDF1C86.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3CCE213C8D987695.mvfrm","index":113}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3CCE213C8D987695.mvfrm","displayName":"Checking for moved APIs","index":113}
{"msg":"noderesult","processed_node_count":93,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_9A1191F70F63F6BB.mvfrm","index":108,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.UnityCurlModule.dll_9A1191F70F63F6BB.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_02AC65D6454A1D51.mvfrm","index":114}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_02AC65D6454A1D51.mvfrm","displayName":"Checking for moved APIs","index":114}
{"msg":"noderesult","processed_node_count":94,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_8B820BD2AC6A239F.mvfrm","index":107,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.UnityConnectModule.dll_8B820BD2AC6A239F.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_A7D7A27FD520B061.mvfrm","index":115}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_A7D7A27FD520B061.mvfrm","displayName":"Checking for moved APIs","index":115}
{"msg":"noderesult","processed_node_count":95,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_8DDE3021FF9F8D2B.mvfrm","index":106,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.UnityAnalyticsModule.dll_8DDE3021FF9F8D2B.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_E3DEF0019A378312.mvfrm","index":116}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_E3DEF0019A378312.mvfrm","displayName":"Checking for moved APIs","index":116}
{"msg":"noderesult","processed_node_count":96,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_AEAC1BB5544068A9.mvfrm","index":109,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.UnityTestProtocolModule.dll_AEAC1BB5544068A9.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_4191D5C1BDD09DE5.mvfrm","index":117}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_4191D5C1BDD09DE5.mvfrm","displayName":"Checking for moved APIs","index":117}
{"msg":"noderesult","processed_node_count":97,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_AE12D3B1B24D183C.mvfrm","index":110,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.UnityWebRequestAssetBundleModule.dll_AE12D3B1B24D183C.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_5D919F001659B1C7.mvfrm","index":118}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_5D919F001659B1C7.mvfrm","displayName":"Checking for moved APIs","index":118}
{"msg":"noderesult","processed_node_count":98,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_95038191186148C0.mvfrm","index":111,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.UnityWebRequestAudioModule.dll_95038191186148C0.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_A761188D10ACF2F6.mvfrm","index":119}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_A761188D10ACF2F6.mvfrm","displayName":"Checking for moved APIs","index":119}
{"msg":"noderesult","processed_node_count":99,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_08577EFB2D8C4EDA.mvfrm","index":112,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.UnityWebRequestModule.dll_08577EFB2D8C4EDA.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_0F47E031DE7CBED2.mvfrm","index":120}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_0F47E031DE7CBED2.mvfrm","displayName":"Checking for moved APIs","index":120}
{"msg":"noderesult","processed_node_count":100,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3CCE213C8D987695.mvfrm","index":113,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.UnityWebRequestTextureModule.dll_3CCE213C8D987695.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_DA1FDB9199E74054.mvfrm","index":121}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_DA1FDB9199E74054.mvfrm","displayName":"Checking for moved APIs","index":121}
{"msg":"noderesult","processed_node_count":101,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_02AC65D6454A1D51.mvfrm","index":114,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.UnityWebRequestWWWModule.dll_02AC65D6454A1D51.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_84D6A8621C493071.mvfrm","index":12}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_84D6A8621C493071.mvfrm","displayName":"Checking for moved APIs","index":12}
{"msg":"noderesult","processed_node_count":102,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_4191D5C1BDD09DE5.mvfrm","index":117,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.VideoModule.dll_4191D5C1BDD09DE5.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_8519B72F30B7EEE3.mvfrm","index":11}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_8519B72F30B7EEE3.mvfrm","displayName":"Checking for moved APIs","index":11}
{"msg":"noderesult","processed_node_count":103,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_A7D7A27FD520B061.mvfrm","index":115,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.VehiclesModule.dll_A7D7A27FD520B061.mvfrm"}
{"msg":"noderesult","processed_node_count":104,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_E3DEF0019A378312.mvfrm","index":116,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.VFXModule.dll_E3DEF0019A378312.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_CA3B36F6C19A89E9.mvfrm","index":9}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_CA3B36F6C19A89E9.mvfrm","displayName":"Checking for moved APIs","index":9}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_75D831B6A540D53E.mvfrm","index":10}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_75D831B6A540D53E.mvfrm","displayName":"Checking for moved APIs","index":10}
{"msg":"noderesult","processed_node_count":105,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_A761188D10ACF2F6.mvfrm","index":119,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.VRModule.dll_A761188D10ACF2F6.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_A2E87A4D8CFE3DDA.mvfrm","index":8}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_A2E87A4D8CFE3DDA.mvfrm","displayName":"Checking for moved APIs","index":8}
{"msg":"noderesult","processed_node_count":106,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_5D919F001659B1C7.mvfrm","index":118,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.VirtualTexturingModule.dll_5D919F001659B1C7.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_4A503D7C9B535AEF.mvfrm","index":7}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_4A503D7C9B535AEF.mvfrm","displayName":"Checking for moved APIs","index":7}
{"msg":"noderesult","processed_node_count":107,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_0F47E031DE7CBED2.mvfrm","index":120,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.WindModule.dll_0F47E031DE7CBED2.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_B009007CB866ED03.mvfrm","index":5}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_B009007CB866ED03.mvfrm","displayName":"Checking for moved APIs","index":5}
{"msg":"noderesult","processed_node_count":108,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_DA1FDB9199E74054.mvfrm","index":121,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.XRModule.dll_DA1FDB9199E74054.mvfrm"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt","index":1}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt","displayName":"Writing UnityEngine.TestRunner.UnityAdditionalFile.txt","index":1}
{"msg":"noderesult","processed_node_count":109,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt","index":1,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.TestRunner.UnityAdditionalFile.txt"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp2","index":3}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp2","displayName":"Writing UnityEngine.TestRunner.rsp2","index":3}
{"msg":"noderesult","processed_node_count":110,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp2","index":3,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.TestRunner.rsp2"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp","index":122}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp","displayName":"Writing UnityEngine.TestRunner.dll.mvfrm.rsp","index":122}
{"msg":"noderesult","processed_node_count":111,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp","index":122,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.TestRunner.dll.mvfrm.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp","index":2}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp","displayName":"Writing UnityEngine.TestRunner.rsp","index":2}
{"msg":"noderesult","processed_node_count":112,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp","index":2,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.TestRunner.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt","index":124}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt","displayName":"Writing UnityEngine.UI.UnityAdditionalFile.txt","index":124}
{"msg":"noderesult","processed_node_count":113,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt","index":124,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.UI.UnityAdditionalFile.txt"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2","index":126}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2","displayName":"Writing UnityEngine.UI.rsp2","index":126}
{"msg":"noderesult","processed_node_count":114,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2","index":126,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.UI.rsp2"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_D6C87365A8B0A170.mvfrm","index":128}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_D6C87365A8B0A170.mvfrm","displayName":"Checking for moved APIs","index":128}
{"msg":"noderesult","processed_node_count":115,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_84D6A8621C493071.mvfrm","index":12,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.DiagnosticsModule.dll_84D6A8621C493071.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_22DA848A55AE3E42.mvfrm","index":129}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_22DA848A55AE3E42.mvfrm","displayName":"Checking for moved APIs","index":129}
{"msg":"noderesult","processed_node_count":116,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_CA3B36F6C19A89E9.mvfrm","index":9,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.CoreBusinessMetricsModule.dll_CA3B36F6C19A89E9.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_8189EC8801BEEB18.mvfrm","index":135}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_8189EC8801BEEB18.mvfrm","displayName":"Checking for moved APIs","index":135}
{"msg":"noderesult","processed_node_count":117,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_8519B72F30B7EEE3.mvfrm","index":11,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.DeviceSimulatorModule.dll_8519B72F30B7EEE3.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_7F371AF1A9E7F3FC.mvfrm","index":131}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_7F371AF1A9E7F3FC.mvfrm","displayName":"Checking for moved APIs","index":131}
{"msg":"noderesult","processed_node_count":118,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_75D831B6A540D53E.mvfrm","index":10,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.CoreModule.dll_75D831B6A540D53E.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_1280E01E43015E29.mvfrm","index":130}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_1280E01E43015E29.mvfrm","displayName":"Checking for moved APIs","index":130}
{"msg":"noderesult","processed_node_count":119,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_4A503D7C9B535AEF.mvfrm","index":7,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.AdaptivePerformanceModule.dll_4A503D7C9B535AEF.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_673AEEFA91C338F1.mvfrm","index":134}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_673AEEFA91C338F1.mvfrm","displayName":"Checking for moved APIs","index":134}
{"msg":"noderesult","processed_node_count":120,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_B009007CB866ED03.mvfrm","index":5,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.AccessibilityModule.dll_B009007CB866ED03.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_D01CC5A6D0F5DAF9.mvfrm","index":133}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_D01CC5A6D0F5DAF9.mvfrm","displayName":"Checking for moved APIs","index":133}
{"msg":"noderesult","processed_node_count":121,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_A2E87A4D8CFE3DDA.mvfrm","index":8,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.BuildProfileModule.dll_A2E87A4D8CFE3DDA.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm","index":123}
{"msg":"runNodeAction","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm","displayName":"Checking for moved APIs","index":123}
{"msg":"noderesult","processed_node_count":122,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_D6C87365A8B0A170.mvfrm","index":128,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.Graphs.dll_D6C87365A8B0A170.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_28DAF9479429A673.mvfrm","index":132}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_28DAF9479429A673.mvfrm","displayName":"Checking for moved APIs","index":132}
{"msg":"noderesult","processed_node_count":123,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_8189EC8801BEEB18.mvfrm","index":135,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\Unity.VisualScripting.Antlr3.Runtime.dll_8189EC8801BEEB18.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_6470942D9C999E52.mvfrm","index":143}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_6470942D9C999E52.mvfrm","displayName":"Checking for moved APIs","index":143}
{"msg":"noderesult","processed_node_count":124,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_673AEEFA91C338F1.mvfrm","index":134,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\Unity.VisualScripting.TextureAssets.dll_673AEEFA91C338F1.mvfrm"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2","index":140}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2","displayName":"Writing UnityEditor.TestRunner.rsp2","index":140}
{"msg":"noderesult","processed_node_count":125,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2","index":140,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.TestRunner.rsp2"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt","index":138}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt","displayName":"Writing UnityEditor.TestRunner.UnityAdditionalFile.txt","index":138}
{"msg":"noderesult","processed_node_count":126,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt","index":138,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.TestRunner.UnityAdditionalFile.txt"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_5EE0364917308BF5.mvfrm","index":144}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_5EE0364917308BF5.mvfrm","displayName":"Checking for moved APIs","index":144}
{"msg":"noderesult","processed_node_count":127,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_7F371AF1A9E7F3FC.mvfrm","index":131,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\Unity.Plastic.Newtonsoft.Json.dll_7F371AF1A9E7F3FC.mvfrm"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp","index":136}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp","displayName":"Writing UnityEngine.UI.dll.mvfrm.rsp","index":136}
{"msg":"noderesult","processed_node_count":128,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp","index":136,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.UI.dll.mvfrm.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp","index":125}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp","displayName":"Writing UnityEngine.UI.rsp","index":125}
{"msg":"noderesult","processed_node_count":129,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp","index":125,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.UI.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp","index":145}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp","displayName":"Writing UnityEditor.TestRunner.dll.mvfrm.rsp","index":145}
{"msg":"noderesult","processed_node_count":130,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp","index":145,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.TestRunner.dll.mvfrm.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp","index":139}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp","displayName":"Writing UnityEditor.TestRunner.rsp","index":139}
{"msg":"noderesult","processed_node_count":131,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp","index":139,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.TestRunner.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt","index":147}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt","displayName":"Writing UnityEditor.UI.UnityAdditionalFile.txt","index":147}
{"msg":"noderesult","processed_node_count":132,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt","index":147,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.UI.UnityAdditionalFile.txt"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp2","index":149}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp2","displayName":"Writing UnityEditor.UI.rsp2","index":149}
{"msg":"noderesult","processed_node_count":133,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp2","index":149,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.UI.rsp2"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp","index":153}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp","displayName":"Writing UnityEditor.UI.dll.mvfrm.rsp","index":153}
{"msg":"noderesult","processed_node_count":134,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp","index":153,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.UI.dll.mvfrm.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp","index":148}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp","displayName":"Writing UnityEditor.UI.rsp","index":148}
{"msg":"noderesult","processed_node_count":135,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp","index":148,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.UI.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt","index":216}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt","displayName":"Writing Unity.VisualScripting.Core.UnityAdditionalFile.txt","index":216}
{"msg":"noderesult","processed_node_count":136,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt","index":216,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Core.UnityAdditionalFile.txt"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp2","index":218}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp2","displayName":"Writing Unity.VisualScripting.Core.rsp2","index":218}
{"msg":"noderesult","processed_node_count":137,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp2","index":218,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Core.rsp2"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp","index":220}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp","displayName":"Writing Unity.VisualScripting.Core.dll.mvfrm.rsp","index":220}
{"msg":"noderesult","processed_node_count":138,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_1280E01E43015E29.mvfrm","index":130,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\Unity.Plastic.Antlr3.Runtime.dll_1280E01E43015E29.mvfrm"}
{"msg":"noderesult","processed_node_count":138,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_D01CC5A6D0F5DAF9.mvfrm","index":133,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\Unity.VisualScripting.YamlDotNet.dll_D01CC5A6D0F5DAF9.mvfrm"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp","index":217}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp","displayName":"Writing Unity.VisualScripting.Core.rsp","index":217}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt","index":272}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt","displayName":"Writing Unity.VisualScripting.Flow.UnityAdditionalFile.txt","index":272}
{"msg":"noderesult","processed_node_count":140,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_22DA848A55AE3E42.mvfrm","index":129,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.WindowsStandalone.Extensions.dll_22DA848A55AE3E42.mvfrm"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp2","index":274}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp2","displayName":"Writing Unity.VisualScripting.Flow.rsp2","index":274}
{"msg":"noderesult","processed_node_count":141,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp","index":220,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Core.dll.mvfrm.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt","index":265}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt","displayName":"Writing Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt","index":265}
{"msg":"noderesult","processed_node_count":142,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt","index":272,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}
{"msg":"noderesult","processed_node_count":142,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp2","index":274,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Flow.rsp2"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2","index":267}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2","displayName":"Writing Unity.VisualScripting.Core.Editor.rsp2","index":267}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp","index":276}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp","displayName":"Writing Unity.VisualScripting.Flow.dll.mvfrm.rsp","index":276}
{"msg":"noderesult","processed_node_count":144,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp","index":217,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Core.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp","index":270}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp","displayName":"Writing Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp","index":270}
{"msg":"noderesult","processed_node_count":145,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt","index":265,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}
{"msg":"noderesult","processed_node_count":146,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2","index":267,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Core.Editor.rsp2"}
{"msg":"noderesult","processed_node_count":146,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp","index":276,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Flow.dll.mvfrm.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt","index":278}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt","displayName":"Writing Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt","index":278}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp","index":266}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp","displayName":"Writing Unity.VisualScripting.Core.Editor.rsp","index":266}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp","index":273}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp","displayName":"Writing Unity.VisualScripting.Flow.rsp","index":273}
{"msg":"noderesult","processed_node_count":148,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp","index":270,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2","index":280}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2","displayName":"Writing Unity.VisualScripting.Flow.Editor.rsp2","index":280}
{"msg":"noderesult","processed_node_count":149,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt","index":278,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt","index":286}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt","displayName":"Writing Unity.VisualScripting.State.UnityAdditionalFile.txt","index":286}
{"msg":"noderesult","processed_node_count":150,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp","index":266,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Core.Editor.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp2","index":288}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp2","displayName":"Writing Unity.VisualScripting.State.rsp2","index":288}
{"msg":"noderesult","processed_node_count":151,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp","index":273,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Flow.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp","index":284}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp","displayName":"Writing Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp","index":284}
{"msg":"noderesult","processed_node_count":152,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2","index":280,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Flow.Editor.rsp2"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp","index":290}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp","displayName":"Writing Unity.VisualScripting.State.dll.mvfrm.rsp","index":290}
{"msg":"noderesult","processed_node_count":153,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt","index":286,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.State.UnityAdditionalFile.txt"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp","index":279}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp","displayName":"Writing Unity.VisualScripting.Flow.Editor.rsp","index":279}
{"msg":"noderesult","processed_node_count":154,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp2","index":288,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.State.rsp2"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp","index":287}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp","displayName":"Writing Unity.VisualScripting.State.rsp","index":287}
{"msg":"noderesult","processed_node_count":155,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp","index":290,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.State.dll.mvfrm.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt","index":300}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt","displayName":"Writing Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt","index":300}
{"msg":"noderesult","processed_node_count":156,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp","index":284,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp2","index":302}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp2","displayName":"Writing Unity.VisualScripting.State.Editor.rsp2","index":302}
{"msg":"noderesult","processed_node_count":157,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp","index":279,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Flow.Editor.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.UnityAdditionalFile.txt","index":198}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.UnityAdditionalFile.txt","displayName":"Writing Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.UnityAdditionalFile.txt","index":198}
{"msg":"noderesult","processed_node_count":158,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp","index":287,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.State.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp2","index":200}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp2","displayName":"Writing Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp2","index":200}
{"msg":"noderesult","processed_node_count":159,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt","index":300,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt","index":162}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt","displayName":"Writing Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt","index":162}
{"msg":"noderesult","processed_node_count":160,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.UnityAdditionalFile.txt","index":198,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.UnityAdditionalFile.txt"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt","index":204}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt","displayName":"Writing Unity.TextMeshPro.UnityAdditionalFile.txt","index":204}
{"msg":"noderesult","processed_node_count":161,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp2","index":302,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.State.Editor.rsp2"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp2","index":164}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp2","displayName":"Writing Unity.Multiplayer.Center.Common.rsp2","index":164}
{"msg":"noderesult","processed_node_count":162,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp2","index":200,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp2"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp2","index":206}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp2","displayName":"Writing Unity.TextMeshPro.rsp2","index":206}
{"msg":"noderesult","processed_node_count":163,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt","index":204,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TextMeshPro.UnityAdditionalFile.txt"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.UnityAdditionalFile.txt","index":186}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.UnityAdditionalFile.txt","displayName":"Writing Unity.Settings.Editor.UnityAdditionalFile.txt","index":186}
{"msg":"noderesult","processed_node_count":164,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp2","index":206,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TextMeshPro.rsp2"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp2","index":188}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp2","displayName":"Writing Unity.Settings.Editor.rsp2","index":188}
{"msg":"noderesult","processed_node_count":165,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt","index":162,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"}
{"msg":"noderesult","processed_node_count":165,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.UnityAdditionalFile.txt","index":186,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Settings.Editor.UnityAdditionalFile.txt"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt","index":210}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.UnityAdditionalFile.txt","index":192}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt","displayName":"Writing Unity.Timeline.UnityAdditionalFile.txt","index":210}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.UnityAdditionalFile.txt","displayName":"Writing Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.UnityAdditionalFile.txt","index":192}
{"msg":"noderesult","processed_node_count":167,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp2","index":164,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Multiplayer.Center.Common.rsp2"}
{"msg":"noderesult","processed_node_count":167,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp2","index":188,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Settings.Editor.rsp2"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2","index":212}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2","displayName":"Writing Unity.Timeline.rsp2","index":212}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp2","index":194}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp2","displayName":"Writing Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp2","index":194}
{"msg":"noderesult","processed_node_count":169,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.UnityAdditionalFile.txt","index":192,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.UnityAdditionalFile.txt"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm.rsp","index":196}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm.rsp","displayName":"Writing Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm.rsp","index":196}
{"msg":"noderesult","processed_node_count":170,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp2","index":194,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp2"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp","index":208}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp","displayName":"Writing Unity.TextMeshPro.dll.mvfrm.rsp","index":208}
{"msg":"noderesult","processed_node_count":171,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt","index":210,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Timeline.UnityAdditionalFile.txt"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp","index":166}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp","displayName":"Writing Unity.Multiplayer.Center.Common.dll.mvfrm.rsp","index":166}
{"msg":"noderesult","processed_node_count":172,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2","index":212,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Timeline.rsp2"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm.rsp","index":190}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm.rsp","displayName":"Writing Unity.Settings.Editor.dll.mvfrm.rsp","index":190}
{"msg":"noderesult","processed_node_count":173,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm.rsp","index":196,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp","index":304}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp","displayName":"Writing Unity.VisualScripting.State.Editor.dll.mvfrm.rsp","index":304}
{"msg":"noderesult","processed_node_count":174,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp","index":208,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TextMeshPro.dll.mvfrm.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm.rsp","index":214}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm.rsp","displayName":"Writing Unity.Timeline.dll.mvfrm.rsp","index":214}
{"msg":"noderesult","processed_node_count":175,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp","index":166,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Multiplayer.Center.Common.dll.mvfrm.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm.rsp","index":202}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm.rsp","displayName":"Writing Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm.rsp","index":202}
{"msg":"noderesult","processed_node_count":176,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm.rsp","index":190,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Settings.Editor.dll.mvfrm.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp","index":211}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp","displayName":"Writing Unity.Timeline.rsp","index":211}
{"msg":"noderesult","processed_node_count":177,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp","index":304,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp","index":163}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp","displayName":"Writing Unity.Multiplayer.Center.Common.rsp","index":163}
{"msg":"noderesult","processed_node_count":178,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm.rsp","index":214,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Timeline.dll.mvfrm.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp","index":193}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp","displayName":"Writing Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp","index":193}
{"msg":"noderesult","processed_node_count":179,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp","index":211,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Timeline.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp","index":187}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp","displayName":"Writing Unity.Settings.Editor.rsp","index":187}
{"msg":"noderesult","processed_node_count":180,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp","index":163,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Multiplayer.Center.Common.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp","index":199}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp","displayName":"Writing Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp","index":199}
{"msg":"noderesult","processed_node_count":181,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm.rsp","index":202,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp","index":301}
{"msg":"noderesult","processed_node_count":182,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp","index":193,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp","displayName":"Writing Unity.VisualScripting.State.Editor.rsp","index":301}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp","index":205}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp","displayName":"Writing Unity.TextMeshPro.rsp","index":205}
{"msg":"noderesult","processed_node_count":183,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp","index":187,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Settings.Editor.rsp"}
{"msg":"noderesult","processed_node_count":183,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp","index":199,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp2","index":176}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp2","displayName":"Writing Unity.PlasticSCM.Editor.rsp2","index":176}
{"msg":"noderesult","processed_node_count":184,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp","index":205,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TextMeshPro.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt","index":174}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt","displayName":"Writing Unity.PlasticSCM.Editor.UnityAdditionalFile.txt","index":174}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp2","index":244}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp2","displayName":"Writing Unity.TestTools.CodeCoverage.Editor.rsp2","index":244}
{"msg":"noderesult","processed_node_count":186,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp","index":301,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.State.Editor.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.rsp2","index":157}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.rsp2","displayName":"Writing Unity.EditorCoroutines.Editor.rsp2","index":157}
{"msg":"noderesult","processed_node_count":187,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt","index":174,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt","index":235}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt","displayName":"Writing Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt","index":235}
{"msg":"noderesult","processed_node_count":188,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp2","index":244,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TestTools.CodeCoverage.Editor.rsp2"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2","index":237}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2","displayName":"Writing Unity.Multiplayer.Center.Editor.rsp2","index":237}
{"msg":"noderesult","processed_node_count":189,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp2","index":176,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.PlasticSCM.Editor.rsp2"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.UnityAdditionalFile.txt","index":168}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.UnityAdditionalFile.txt","displayName":"Writing Unity.Performance.Profile-Analyzer.Editor.UnityAdditionalFile.txt","index":168}
{"msg":"noderesult","processed_node_count":190,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt","index":235,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt","index":251}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt","displayName":"Writing Unity.TextMeshPro.Editor.UnityAdditionalFile.txt","index":251}
{"msg":"noderesult","processed_node_count":191,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.rsp2","index":157,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.EditorCoroutines.Editor.rsp2"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp2","index":170}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp2","displayName":"Writing Unity.Performance.Profile-Analyzer.Editor.rsp2","index":170}
{"msg":"noderesult","processed_node_count":192,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2","index":237,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Multiplayer.Center.Editor.rsp2"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt","index":180}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt","displayName":"Writing Unity.Rider.Editor.UnityAdditionalFile.txt","index":180}
{"msg":"noderesult","processed_node_count":193,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.UnityAdditionalFile.txt","index":168,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Performance.Profile-Analyzer.Editor.UnityAdditionalFile.txt"}
{"msg":"noderesult","processed_node_count":193,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt","index":251,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp2","index":182}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp2","index":253}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp2","displayName":"Writing Unity.Rider.Editor.rsp2","index":182}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp2","displayName":"Writing Unity.TextMeshPro.Editor.rsp2","index":253}
{"msg":"noderesult","processed_node_count":195,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt","index":180,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Rider.Editor.UnityAdditionalFile.txt"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.UnityAdditionalFile.txt","index":242}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.UnityAdditionalFile.txt","displayName":"Writing Unity.TestTools.CodeCoverage.Editor.UnityAdditionalFile.txt","index":242}
{"msg":"noderesult","processed_node_count":196,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp2","index":170,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Performance.Profile-Analyzer.Editor.rsp2"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt","index":258}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt","displayName":"Writing Unity.Timeline.Editor.UnityAdditionalFile.txt","index":258}
{"msg":"noderesult","processed_node_count":197,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp2","index":182,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Rider.Editor.rsp2"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt","index":292}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt","displayName":"Writing Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt","index":292}
{"msg":"noderesult","processed_node_count":198,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp2","index":253,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TextMeshPro.Editor.rsp2"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2","index":294}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2","displayName":"Writing Unity.VisualScripting.SettingsProvider.Editor.rsp2","index":294}
{"msg":"noderesult","processed_node_count":199,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.UnityAdditionalFile.txt","index":242,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TestTools.CodeCoverage.Editor.UnityAdditionalFile.txt"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2","index":308}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2","displayName":"Writing Unity.VisualScripting.Shared.Editor.rsp2","index":308}
{"msg":"noderesult","processed_node_count":200,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt","index":258,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Timeline.Editor.UnityAdditionalFile.txt"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt","index":222}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt","displayName":"Writing Unity.VisualStudio.Editor.UnityAdditionalFile.txt","index":222}
{"msg":"noderesult","processed_node_count":201,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt","index":292,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp2","index":224}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp2","displayName":"Writing Unity.VisualStudio.Editor.rsp2","index":224}
{"msg":"noderesult","processed_node_count":202,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2","index":294,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.SettingsProvider.Editor.rsp2"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt","index":306}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt","displayName":"Writing Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt","index":306}
{"msg":"noderesult","processed_node_count":203,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt","index":222,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp","index":178}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp","displayName":"Writing Unity.PlasticSCM.Editor.dll.mvfrm.rsp","index":178}
{"msg":"noderesult","processed_node_count":204,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2","index":308,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Shared.Editor.rsp2"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.UnityAdditionalFile.txt","index":155}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.UnityAdditionalFile.txt","displayName":"Writing Unity.EditorCoroutines.Editor.UnityAdditionalFile.txt","index":155}
{"msg":"noderesult","processed_node_count":205,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp2","index":224,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualStudio.Editor.rsp2"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2","index":260}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2","displayName":"Writing Unity.Timeline.Editor.rsp2","index":260}
{"msg":"noderesult","processed_node_count":206,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt","index":306,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp","index":311}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp","displayName":"Writing Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp","index":311}
{"msg":"noderesult","processed_node_count":207,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp","index":178,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp","index":263}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp","displayName":"Writing Unity.Timeline.Editor.dll.mvfrm.rsp","index":263}
{"msg":"noderesult","processed_node_count":208,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2","index":260,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Timeline.Editor.rsp2"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp","index":256}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp","displayName":"Writing Unity.TextMeshPro.Editor.dll.mvfrm.rsp","index":256}
{"msg":"noderesult","processed_node_count":209,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.UnityAdditionalFile.txt","index":155,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.EditorCoroutines.Editor.UnityAdditionalFile.txt"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp","index":226}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp","displayName":"Writing Unity.VisualStudio.Editor.dll.mvfrm.rsp","index":226}
{"msg":"noderesult","processed_node_count":210,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp","index":226,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualStudio.Editor.dll.mvfrm.rsp"}
{"msg":"noderesult","processed_node_count":210,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp","index":256,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp","index":184}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp","displayName":"Writing Unity.Rider.Editor.dll.mvfrm.rsp","index":184}
{"msg":"noderesult","processed_node_count":212,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp","index":311,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm.rsp","index":249}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm.rsp","displayName":"Writing Unity.TestTools.CodeCoverage.Editor.dll.mvfrm.rsp","index":249}
{"msg":"noderesult","processed_node_count":212,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp","index":263,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Timeline.Editor.dll.mvfrm.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm.rsp","index":160}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm.rsp","displayName":"Writing Unity.EditorCoroutines.Editor.dll.mvfrm.rsp","index":160}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp","index":175}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp","displayName":"Writing Unity.PlasticSCM.Editor.rsp","index":175}
{"msg":"noderesult","processed_node_count":214,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp","index":184,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Rider.Editor.dll.mvfrm.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp","index":298}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp","displayName":"Writing Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp","index":298}
{"msg":"noderesult","processed_node_count":215,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm.rsp","index":249,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TestTools.CodeCoverage.Editor.dll.mvfrm.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp","index":240}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp","displayName":"Writing Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp","index":240}
{"msg":"noderesult","processed_node_count":216,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp","index":175,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.PlasticSCM.Editor.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm.rsp","index":172}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm.rsp","displayName":"Writing Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm.rsp","index":172}
{"msg":"noderesult","processed_node_count":217,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm.rsp","index":160,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.EditorCoroutines.Editor.dll.mvfrm.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp","index":223}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp","displayName":"Writing Unity.VisualStudio.Editor.rsp","index":223}
{"msg":"noderesult","processed_node_count":218,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp","index":298,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp","index":181}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp","displayName":"Writing Unity.Rider.Editor.rsp","index":181}
{"msg":"noderesult","processed_node_count":219,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp","index":240,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp","index":293}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp","displayName":"Writing Unity.VisualScripting.SettingsProvider.Editor.rsp","index":293}
{"msg":"noderesult","processed_node_count":220,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp","index":181,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Rider.Editor.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.rsp","index":156}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.rsp","displayName":"Writing Unity.EditorCoroutines.Editor.rsp","index":156}
{"msg":"noderesult","processed_node_count":221,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm.rsp","index":172,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp","index":236}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp","displayName":"Writing Unity.Multiplayer.Center.Editor.rsp","index":236}
{"msg":"noderesult","processed_node_count":222,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp","index":293,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.SettingsProvider.Editor.rsp"}
{"msg":"noderesult","processed_node_count":223,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp","index":223,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualStudio.Editor.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp","index":243}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp","displayName":"Writing Unity.TestTools.CodeCoverage.Editor.rsp","index":243}
{"msg":"noderesult","processed_node_count":224,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.rsp","index":156,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.EditorCoroutines.Editor.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp","index":307}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp","displayName":"Writing Unity.VisualScripting.Shared.Editor.rsp","index":307}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp","index":259}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp","displayName":"Writing Unity.Timeline.Editor.rsp","index":259}
{"msg":"noderesult","processed_node_count":225,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp","index":259,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Timeline.Editor.rsp"}
{"msg":"noderesult","processed_node_count":225,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp","index":243,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TestTools.CodeCoverage.Editor.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp","index":169}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp","displayName":"Writing Unity.Performance.Profile-Analyzer.Editor.rsp","index":169}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp","index":252}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp","displayName":"Writing Unity.TextMeshPro.Editor.rsp","index":252}
{"msg":"noderesult","processed_node_count":227,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp","index":236,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Multiplayer.Center.Editor.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp","index":327}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp","displayName":"Writing Assembly-CSharp.dll.mvfrm.rsp","index":327}
{"msg":"noderesult","processed_node_count":228,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp","index":307,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Shared.Editor.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt","index":313}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt","displayName":"Writing Assembly-CSharp.UnityAdditionalFile.txt","index":313}
{"msg":"noderesult","processed_node_count":229,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp","index":252,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TextMeshPro.Editor.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp2","index":315}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp2","displayName":"Writing Assembly-CSharp.rsp2","index":315}
{"msg":"noderesult","processed_node_count":230,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp","index":169,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Performance.Profile-Analyzer.Editor.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp2","index":230}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp2","displayName":"Writing Unity.CollabProxy.Editor.rsp2","index":230}
{"msg":"noderesult","processed_node_count":231,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp","index":327,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll.mvfrm.rsp"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp","index":233}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp","displayName":"Writing Unity.CollabProxy.Editor.dll.mvfrm.rsp","index":233}
{"msg":"noderesult","processed_node_count":232,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp2","index":315,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.rsp2"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt","index":228}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt","displayName":"Writing Unity.CollabProxy.Editor.UnityAdditionalFile.txt","index":228}
{"msg":"noderesult","processed_node_count":233,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt","index":313,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.UnityAdditionalFile.txt"}
{"msg":"noderesult","processed_node_count":233,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp2","index":230,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.CollabProxy.Editor.rsp2"}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp","index":229}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp","displayName":"Writing Unity.CollabProxy.Editor.rsp","index":229}
{"msg":"newNode","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp","index":314}
{"msg":"runNodeAction","annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp","displayName":"Writing Assembly-CSharp.rsp","index":314}
{"msg":"noderesult","processed_node_count":235,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt","index":228,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}
{"msg":"noderesult","processed_node_count":236,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp","index":229,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.CollabProxy.Editor.rsp"}
{"msg":"noderesult","processed_node_count":237,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp","index":314,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.rsp"}
{"msg":"noderesult","processed_node_count":238,"number_of_nodes_ever_queued":386,"annotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp","index":233,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.CollabProxy.Editor.dll.mvfrm.rsp"}
{"msg":"noderesult","processed_node_count":239,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm","index":123,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.TestRunner.dll.mvfrm"}
{"msg":"noderesult","processed_node_count":240,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_28DAF9479429A673.mvfrm","index":132,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\Unity.VisualScripting.IonicZip.dll_28DAF9479429A673.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm","index":137}
{"msg":"runNodeAction","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm","displayName":"Checking for moved APIs","index":137}
{"msg":"newNode","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)","index":4}
{"msg":"noderesult","processed_node_count":241,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_6470942D9C999E52.mvfrm","index":143,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.AMDModule.dll_6470942D9C999E52.mvfrm"}
{"msg":"noderesult","processed_node_count":242,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_5EE0364917308BF5.mvfrm","index":144,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.NVIDIAModule.dll_5EE0364917308BF5.mvfrm"}
{"msg":"cachemiss","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)","index":4,"inputSignature":"779248f75385032777757dc4c5d8d356"}
{"msg":"runNodeAction","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)","displayName":"Compiling C# (UnityEngine.TestRunner)","index":4}
{"msg":"noderesult","processed_node_count":243,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm","index":137,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.UI.dll.mvfrm"}
{"msg":"newNode","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)","index":127}
{"msg":"cachemiss","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)","index":127,"inputSignature":"0d24dbe53a39cf1c078d64e9722754b8"}
{"msg":"runNodeAction","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)","displayName":"Compiling C# (UnityEngine.UI)","index":127}
{"msg":"noderesult","processed_node_count":244,"number_of_nodes_ever_queued":386,"annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)","index":4,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.TestRunner.dll"}
{"msg":"noderesult","processed_node_count":244,"number_of_nodes_ever_queued":386,"annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)","index":127,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.UI.dll"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_E55D0F7C63F01D9E.mvfrm","index":142}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_E55D0F7C63F01D9E.mvfrm","displayName":"Checking for moved APIs","index":142}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.dll","index":329}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.dll","displayName":"Copying UnityEngine.TestRunner.dll","index":329}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.pdb","index":330}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.pdb","displayName":"Copying UnityEngine.TestRunner.pdb","index":330}
{"msg":"noderesult","processed_node_count":245,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.pdb","index":330,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\UnityEngine.TestRunner.pdb"}
{"msg":"noderesult","processed_node_count":246,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.dll","index":329,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\UnityEngine.TestRunner.dll"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_08FEAA520A2EFD60.mvfrm","index":152}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_08FEAA520A2EFD60.mvfrm","displayName":"Checking for moved APIs","index":152}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/UnityEngine.UI.pdb","index":338}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/UnityEngine.UI.pdb","displayName":"Copying UnityEngine.UI.pdb","index":338}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/UnityEngine.UI.dll","index":337}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/UnityEngine.UI.dll","displayName":"Copying UnityEngine.UI.dll","index":337}
{"msg":"noderesult","processed_node_count":248,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/UnityEngine.UI.pdb","index":338,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\UnityEngine.UI.pdb"}
{"msg":"noderesult","processed_node_count":249,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/UnityEngine.UI.dll","index":337,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\UnityEngine.UI.dll"}
{"msg":"noderesult","processed_node_count":250,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_08FEAA520A2EFD60.mvfrm","index":152,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.UI.ref.dll_08FEAA520A2EFD60.mvfrm"}
{"msg":"noderesult","processed_node_count":251,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_E55D0F7C63F01D9E.mvfrm","index":142,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.TestRunner.ref.dll_E55D0F7C63F01D9E.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm","index":146}
{"msg":"runNodeAction","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm","displayName":"Checking for moved APIs","index":146}
{"msg":"noderesult","processed_node_count":252,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm","index":146,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.TestRunner.dll.mvfrm"}
{"msg":"newNode","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)","index":141}
{"msg":"cachemiss","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)","index":141,"inputSignature":"f74ab2dcb3e4002e9385a99515d997df"}
{"msg":"runNodeAction","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)","displayName":"Compiling C# (UnityEditor.TestRunner)","index":141}
{"msg":"noderesult","processed_node_count":253,"number_of_nodes_ever_queued":386,"annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)","index":141,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.TestRunner.dll"}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.pdb","index":332}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_193EC4CE382CBFB3.mvfrm","index":151}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.pdb","displayName":"Copying UnityEditor.TestRunner.pdb","index":332}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_193EC4CE382CBFB3.mvfrm","displayName":"Checking for moved APIs","index":151}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.dll","index":331}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.dll","displayName":"Copying UnityEditor.TestRunner.dll","index":331}
{"msg":"noderesult","processed_node_count":254,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.pdb","index":332,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\UnityEditor.TestRunner.pdb"}
{"msg":"noderesult","processed_node_count":255,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.dll","index":331,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\UnityEditor.TestRunner.dll"}
{"msg":"noderesult","processed_node_count":256,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_193EC4CE382CBFB3.mvfrm","index":151,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.TestRunner.ref.dll_193EC4CE382CBFB3.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm","index":154}
{"msg":"runNodeAction","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm","displayName":"Checking for moved APIs","index":154}
{"msg":"noderesult","processed_node_count":257,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm","index":154,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.UI.dll.mvfrm"}
{"msg":"newNode","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)","index":150}
{"msg":"cachemiss","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)","index":150,"inputSignature":"261e4f72e11bc31e9d51faf9797a89c6"}
{"msg":"runNodeAction","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)","displayName":"Compiling C# (UnityEditor.UI)","index":150}
{"msg":"noderesult","processed_node_count":258,"number_of_nodes_ever_queued":386,"annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)","index":150,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.UI.dll"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_4C98D3F7040CD4F5.mvfrm","index":159}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_4C98D3F7040CD4F5.mvfrm","displayName":"Checking for moved APIs","index":159}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/UnityEditor.UI.pdb","index":340}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/UnityEditor.UI.pdb","displayName":"Copying UnityEditor.UI.pdb","index":340}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/UnityEditor.UI.dll","index":339}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/UnityEditor.UI.dll","displayName":"Copying UnityEditor.UI.dll","index":339}
{"msg":"noderesult","processed_node_count":259,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/UnityEditor.UI.pdb","index":340,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\UnityEditor.UI.pdb"}
{"msg":"noderesult","processed_node_count":260,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/UnityEditor.UI.dll","index":339,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\UnityEditor.UI.dll"}
{"msg":"noderesult","processed_node_count":261,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_4C98D3F7040CD4F5.mvfrm","index":159,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.UI.ref.dll_4C98D3F7040CD4F5.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm","index":221}
{"msg":"runNodeAction","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm","displayName":"Checking for moved APIs","index":221}
{"msg":"newNode","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm","index":191}
{"msg":"runNodeAction","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm","displayName":"Checking for moved APIs","index":191}
{"msg":"newNode","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm","index":197}
{"msg":"runNodeAction","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm","displayName":"Checking for moved APIs","index":197}
{"msg":"newNode","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm","index":203}
{"msg":"runNodeAction","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm","displayName":"Checking for moved APIs","index":203}
{"msg":"newNode","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm","index":167}
{"msg":"runNodeAction","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm","displayName":"Checking for moved APIs","index":167}
{"msg":"newNode","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm","index":179}
{"msg":"runNodeAction","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm","displayName":"Checking for moved APIs","index":179}
{"msg":"newNode","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm","index":209}
{"msg":"runNodeAction","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm","displayName":"Checking for moved APIs","index":209}
{"msg":"newNode","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm","index":215}
{"msg":"runNodeAction","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm","displayName":"Checking for moved APIs","index":215}
{"msg":"noderesult","processed_node_count":262,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm","index":215,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Timeline.dll.mvfrm"}
{"msg":"noderesult","processed_node_count":263,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm","index":203,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}
{"msg":"noderesult","processed_node_count":264,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm","index":221,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Core.dll.mvfrm"}
{"msg":"noderesult","processed_node_count":264,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm","index":167,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Multiplayer.Center.Common.dll.mvfrm"}
{"msg":"noderesult","processed_node_count":266,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm","index":179,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.PlasticSCM.Editor.dll.mvfrm"}
{"msg":"noderesult","processed_node_count":267,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm","index":191,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Settings.Editor.dll.mvfrm"}
{"msg":"noderesult","processed_node_count":268,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm","index":209,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TextMeshPro.dll.mvfrm"}
{"msg":"noderesult","processed_node_count":269,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm","index":197,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}
{"msg":"newNode","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)","index":165}
{"msg":"cachemiss","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)","index":165,"inputSignature":"bc60daf5cbbff0923d130f7dba4692d2"}
{"msg":"runNodeAction","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)","displayName":"Compiling C# (Unity.Multiplayer.Center.Common)","index":165}
{"msg":"newNode","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)","index":201}
{"msg":"cachemiss","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)","index":201,"inputSignature":"ca933312f6c1019b9737d2322a42da38"}
{"msg":"runNodeAction","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)","displayName":"Compiling C# (Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection)","index":201}
{"msg":"newNode","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)","index":195}
{"msg":"cachemiss","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)","index":195,"inputSignature":"21cc380f62a920739a4f9934ae078b24"}
{"msg":"runNodeAction","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)","displayName":"Compiling C# (Unity.TestTools.CodeCoverage.Editor.OpenCover.Model)","index":195}
{"msg":"noderesult","processed_node_count":270,"number_of_nodes_ever_queued":386,"annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)","index":165,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Multiplayer.Center.Common.dll"}
{"msg":"newNode","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)","index":189}
{"msg":"newNode","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)","index":207}
{"msg":"noderesult","processed_node_count":270,"number_of_nodes_ever_queued":386,"annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)","index":201,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_EE36537354EA42C8.mvfrm","index":239}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_EE36537354EA42C8.mvfrm","displayName":"Checking for moved APIs","index":239}
{"msg":"cachemiss","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)","index":189,"inputSignature":"ef8be4e51eea8a5843f546283f0fcd1b"}
{"msg":"runNodeAction","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)","displayName":"Compiling C# (Unity.Settings.Editor)","index":189}
{"msg":"cachemiss","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)","index":207,"inputSignature":"10cd2fbf89d08da56d8783f08abcbcf7"}
{"msg":"runNodeAction","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)","displayName":"Compiling C# (Unity.TextMeshPro)","index":207}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.ref.dll_E668CD773429A040.mvfrm","index":248}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.ref.dll_E668CD773429A040.mvfrm","displayName":"Checking for moved APIs","index":248}
{"msg":"newNode","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)","index":213}
{"msg":"noderesult","processed_node_count":272,"number_of_nodes_ever_queued":386,"annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)","index":195,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll"}
{"msg":"noderesult","processed_node_count":272,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_EE36537354EA42C8.mvfrm","index":239,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\Unity.Multiplayer.Center.Common.ref.dll_EE36537354EA42C8.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm","index":241}
{"msg":"runNodeAction","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm","displayName":"Checking for moved APIs","index":241}
{"msg":"cachemiss","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)","index":213,"inputSignature":"88851b5823c92515b518e8cd9cd2e9bc"}
{"msg":"runNodeAction","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)","displayName":"Compiling C# (Unity.Timeline)","index":213}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.ref.dll_D3B9364D6FB03093.mvfrm","index":247}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.ref.dll_D3B9364D6FB03093.mvfrm","displayName":"Checking for moved APIs","index":247}
{"msg":"noderesult","processed_node_count":274,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.ref.dll_E668CD773429A040.mvfrm","index":248,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.ref.dll_E668CD773429A040.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm","index":173}
{"msg":"runNodeAction","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm","displayName":"Checking for moved APIs","index":173}
{"msg":"noderesult","processed_node_count":275,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm","index":241,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Multiplayer.Center.Editor.dll.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm","index":227}
{"msg":"runNodeAction","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm","displayName":"Checking for moved APIs","index":227}
{"msg":"noderesult","processed_node_count":276,"number_of_nodes_ever_queued":386,"annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)","index":189,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Settings.Editor.dll"}
{"msg":"noderesult","processed_node_count":276,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm","index":173,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm","index":161}
{"msg":"runNodeAction","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm","displayName":"Checking for moved APIs","index":161}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Settings.Editor.ref.dll_7F5B154C05A780B0.mvfrm","index":246}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Settings.Editor.ref.dll_7F5B154C05A780B0.mvfrm","displayName":"Checking for moved APIs","index":246}
{"msg":"noderesult","processed_node_count":278,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.ref.dll_D3B9364D6FB03093.mvfrm","index":247,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.ref.dll_D3B9364D6FB03093.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm","index":185}
{"msg":"runNodeAction","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm","displayName":"Checking for moved APIs","index":185}
{"msg":"noderesult","processed_node_count":279,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm","index":227,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualStudio.Editor.dll.mvfrm"}
{"msg":"noderesult","processed_node_count":280,"number_of_nodes_ever_queued":386,"annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)","index":207,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TextMeshPro.dll"}
{"msg":"noderesult","processed_node_count":280,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm","index":161,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.EditorCoroutines.Editor.dll.mvfrm"}
{"msg":"newNode","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)","index":158}
{"msg":"noderesult","processed_node_count":281,"number_of_nodes_ever_queued":386,"annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)","index":213,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Timeline.dll"}
{"msg":"noderesult","processed_node_count":281,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Settings.Editor.ref.dll_7F5B154C05A780B0.mvfrm","index":246,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\Unity.Settings.Editor.ref.dll_7F5B154C05A780B0.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm","index":250}
{"msg":"runNodeAction","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm","displayName":"Checking for moved APIs","index":250}
{"msg":"noderesult","processed_node_count":282,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm","index":185,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Rider.Editor.dll.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_84A4293DBDD182DB.mvfrm","index":255}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_84A4293DBDD182DB.mvfrm","displayName":"Checking for moved APIs","index":255}
{"msg":"cachemiss","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)","index":158,"inputSignature":"c2f6ca5b717a82310de72a26939b67e5"}
{"msg":"runNodeAction","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)","displayName":"Compiling C# (Unity.EditorCoroutines.Editor)","index":158}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_621CDDF9C514DF8F.mvfrm","index":262}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_621CDDF9C514DF8F.mvfrm","displayName":"Checking for moved APIs","index":262}
{"msg":"newNode","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)","index":219}
{"msg":"newNode","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)","index":177}
{"msg":"noderesult","processed_node_count":285,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm","index":250,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}
{"msg":"noderesult","processed_node_count":286,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_84A4293DBDD182DB.mvfrm","index":255,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\Unity.TextMeshPro.ref.dll_84A4293DBDD182DB.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm","index":257}
{"msg":"runNodeAction","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm","displayName":"Checking for moved APIs","index":257}
{"msg":"cachemiss","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)","index":219,"inputSignature":"8a5b2bd770827d5004d07f17d2adb33e"}
{"msg":"runNodeAction","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)","displayName":"Compiling C# (Unity.VisualScripting.Core)","index":219}
{"msg":"cachemiss","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)","index":177,"inputSignature":"deaee92ae4b6a48e86ad2b5dc741d54d"}
{"msg":"runNodeAction","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)","displayName":"Compiling C# (Unity.PlasticSCM.Editor)","index":177}
{"msg":"noderesult","processed_node_count":287,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_621CDDF9C514DF8F.mvfrm","index":262,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\Unity.Timeline.ref.dll_621CDDF9C514DF8F.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm","index":264}
{"msg":"runNodeAction","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm","displayName":"Checking for moved APIs","index":264}
{"msg":"noderesult","processed_node_count":288,"number_of_nodes_ever_queued":386,"annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)","index":158,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.EditorCoroutines.Editor.dll"}
{"msg":"newNode","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)","index":183}
{"msg":"noderesult","processed_node_count":288,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm","index":264,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Timeline.Editor.dll.mvfrm"}
{"msg":"noderesult","processed_node_count":289,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm","index":257,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TextMeshPro.Editor.dll.mvfrm"}
{"msg":"newNode","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)","index":225}
{"msg":"cachemiss","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)","index":183,"inputSignature":"537151a3df1e31d846fb6276ca48796f"}
{"msg":"runNodeAction","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)","displayName":"Compiling C# (Unity.Rider.Editor)","index":183}
{"msg":"newNode","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)","index":245}
{"msg":"cachemiss","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)","index":225,"inputSignature":"d75c77314aa4bf99f7cebdd16869bdf1"}
{"msg":"runNodeAction","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)","displayName":"Compiling C# (Unity.VisualStudio.Editor)","index":225}
{"msg":"cachemiss","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)","index":245,"inputSignature":"58c8dcccd330a587474ce096a5f4e88a"}
{"msg":"runNodeAction","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)","displayName":"Compiling C# (Unity.TestTools.CodeCoverage.Editor)","index":245}
{"msg":"newNode","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)","index":254}
{"msg":"cachemiss","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)","index":254,"inputSignature":"473624bce4c5cd0efe2e13f3100a69d3"}
{"msg":"runNodeAction","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)","displayName":"Compiling C# (Unity.TextMeshPro.Editor)","index":254}
{"msg":"noderesult","processed_node_count":291,"number_of_nodes_ever_queued":386,"annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)","index":245,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TestTools.CodeCoverage.Editor.dll"}
{"msg":"noderesult","processed_node_count":291,"number_of_nodes_ever_queued":386,"annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)","index":183,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Rider.Editor.dll"}
{"msg":"newNode","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)","index":171}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rider.Editor.ref.dll_9B5591808ABA37AF.mvfrm","index":320}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rider.Editor.ref.dll_9B5591808ABA37AF.mvfrm","displayName":"Checking for moved APIs","index":320}
{"msg":"newNode","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)","index":238}
{"msg":"noderesult","processed_node_count":293,"number_of_nodes_ever_queued":386,"annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)","index":225,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualStudio.Editor.dll"}
{"msg":"cachemiss","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)","index":171,"inputSignature":"651c203222968b134374045ad06a1e4c"}
{"msg":"runNodeAction","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)","displayName":"Compiling C# (Unity.Performance.Profile-Analyzer.Editor)","index":171}
{"msg":"newNode","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)","index":261}
{"msg":"cachemiss","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)","index":238,"inputSignature":"72c6d8c9f39b20154efd39e413ba2936"}
{"msg":"runNodeAction","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)","displayName":"Compiling C# (Unity.Multiplayer.Center.Editor)","index":238}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualStudio.Editor.ref.dll_3A975DBA53ABA4AD.mvfrm","index":326}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualStudio.Editor.ref.dll_3A975DBA53ABA4AD.mvfrm","displayName":"Checking for moved APIs","index":326}
{"msg":"cachemiss","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)","index":261,"inputSignature":"2d0124d265f0542495140f409da0ffbe"}
{"msg":"runNodeAction","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)","displayName":"Compiling C# (Unity.Timeline.Editor)","index":261}
{"msg":"noderesult","processed_node_count":294,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rider.Editor.ref.dll_9B5591808ABA37AF.mvfrm","index":320,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\Unity.Rider.Editor.ref.dll_9B5591808ABA37AF.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.ref.dll_F09CF4826CE11CD9.mvfrm","index":321}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.ref.dll_F09CF4826CE11CD9.mvfrm","displayName":"Checking for moved APIs","index":321}
{"msg":"noderesult","processed_node_count":295,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualStudio.Editor.ref.dll_3A975DBA53ABA4AD.mvfrm","index":326,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\Unity.VisualStudio.Editor.ref.dll_3A975DBA53ABA4AD.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.EditorCoroutines.Editor.ref.dll_34ED10A34098B2DB.mvfrm","index":317}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.EditorCoroutines.Editor.ref.dll_34ED10A34098B2DB.mvfrm","displayName":"Checking for moved APIs","index":317}
{"msg":"noderesult","processed_node_count":296,"number_of_nodes_ever_queued":386,"annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)","index":254,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TextMeshPro.Editor.dll"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.Editor.ref.dll_159E061D77A10B86.mvfrm","index":322}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.Editor.ref.dll_159E061D77A10B86.mvfrm","displayName":"Checking for moved APIs","index":322}
{"msg":"noderesult","processed_node_count":297,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.ref.dll_F09CF4826CE11CD9.mvfrm","index":321,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\Unity.TestTools.CodeCoverage.Editor.ref.dll_F09CF4826CE11CD9.mvfrm"}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.Timeline.dll","index":357}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.Timeline.dll","displayName":"Copying Unity.Timeline.dll","index":357}
{"msg":"noderesult","processed_node_count":298,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.Timeline.dll","index":357,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.Timeline.dll"}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.dll","index":355}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.dll","displayName":"Copying Unity.TextMeshPro.dll","index":355}
{"msg":"noderesult","processed_node_count":299,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.dll","index":355,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.TextMeshPro.dll"}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.dll","index":343}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.dll","displayName":"Copying Unity.Multiplayer.Center.Common.dll","index":343}
{"msg":"noderesult","processed_node_count":300,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.dll","index":343,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.Multiplayer.Center.Common.dll"}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll","index":367}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll","displayName":"Copying Unity.TextMeshPro.Editor.dll","index":367}
{"msg":"noderesult","processed_node_count":301,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll","index":367,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.TextMeshPro.Editor.dll"}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb","index":368}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb","displayName":"Copying Unity.TextMeshPro.Editor.pdb","index":368}
{"msg":"noderesult","processed_node_count":302,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb","index":368,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.TextMeshPro.Editor.pdb"}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb","index":336}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb","displayName":"Copying Unity.VisualStudio.Editor.pdb","index":336}
{"msg":"noderesult","processed_node_count":303,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb","index":336,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.VisualStudio.Editor.pdb"}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll","index":335}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll","displayName":"Copying Unity.VisualStudio.Editor.dll","index":335}
{"msg":"noderesult","processed_node_count":304,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll","index":335,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.VisualStudio.Editor.dll"}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.dll","index":333}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.dll","displayName":"Copying Unity.Rider.Editor.dll","index":333}
{"msg":"noderesult","processed_node_count":305,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.dll","index":333,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.Rider.Editor.dll"}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.pdb","index":334}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.pdb","displayName":"Copying Unity.Rider.Editor.pdb","index":334}
{"msg":"noderesult","processed_node_count":306,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.pdb","index":334,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.Rider.Editor.pdb"}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.pdb","index":366}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.pdb","displayName":"Copying Unity.TestTools.CodeCoverage.Editor.pdb","index":366}
{"msg":"noderesult","processed_node_count":307,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.pdb","index":366,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.TestTools.CodeCoverage.Editor.pdb"}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.dll","index":365}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.dll","displayName":"Copying Unity.TestTools.CodeCoverage.Editor.dll","index":365}
{"msg":"noderesult","processed_node_count":308,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.dll","index":365,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.TestTools.CodeCoverage.Editor.dll"}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.dll","index":341}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.dll","displayName":"Copying Unity.EditorCoroutines.Editor.dll","index":341}
{"msg":"noderesult","processed_node_count":309,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.dll","index":341,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.EditorCoroutines.Editor.dll"}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.pdb","index":342}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.pdb","displayName":"Copying Unity.EditorCoroutines.Editor.pdb","index":342}
{"msg":"noderesult","processed_node_count":310,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.pdb","index":342,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.EditorCoroutines.Editor.pdb"}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.Timeline.pdb","index":358}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.Timeline.pdb","displayName":"Copying Unity.Timeline.pdb","index":358}
{"msg":"noderesult","processed_node_count":311,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.Timeline.pdb","index":358,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.Timeline.pdb"}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb","index":356}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb","displayName":"Copying Unity.TextMeshPro.pdb","index":356}
{"msg":"noderesult","processed_node_count":312,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb","index":356,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.TextMeshPro.pdb"}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.dll","index":349}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.dll","displayName":"Copying Unity.Settings.Editor.dll","index":349}
{"msg":"noderesult","processed_node_count":313,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.dll","index":349,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.Settings.Editor.dll"}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.pdb","index":350}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.pdb","displayName":"Copying Unity.Settings.Editor.pdb","index":350}
{"msg":"noderesult","processed_node_count":314,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.pdb","index":350,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.Settings.Editor.pdb"}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll","index":351}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll","displayName":"Copying Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll","index":351}
{"msg":"noderesult","processed_node_count":315,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll","index":351,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll"}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.pdb","index":352}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.pdb","displayName":"Copying Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.pdb","index":352}
{"msg":"noderesult","processed_node_count":316,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.pdb","index":352,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.pdb"}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll","index":353}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll","displayName":"Copying Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll","index":353}
{"msg":"noderesult","processed_node_count":317,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll","index":353,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll"}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.pdb","index":354}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.pdb","displayName":"Copying Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.pdb","index":354}
{"msg":"noderesult","processed_node_count":318,"number_of_nodes_ever_queued":386,"annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)","index":177,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.PlasticSCM.Editor.dll"}
{"msg":"noderesult","processed_node_count":318,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.pdb","index":354,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.pdb"}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb","index":344}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb","displayName":"Copying Unity.Multiplayer.Center.Common.pdb","index":344}
{"msg":"noderesult","processed_node_count":319,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb","index":344,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.Multiplayer.Center.Common.pdb"}
{"msg":"noderesult","processed_node_count":320,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.EditorCoroutines.Editor.ref.dll_34ED10A34098B2DB.mvfrm","index":317,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\Unity.EditorCoroutines.Editor.ref.dll_34ED10A34098B2DB.mvfrm"}
{"msg":"noderesult","processed_node_count":321,"number_of_nodes_ever_queued":386,"annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)","index":238,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Multiplayer.Center.Editor.dll"}
{"msg":"noderesult","processed_node_count":321,"number_of_nodes_ever_queued":386,"annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)","index":219,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Core.dll"}
{"msg":"noderesult","processed_node_count":321,"number_of_nodes_ever_queued":386,"annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)","index":171,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Performance.Profile-Analyzer.Editor.dll"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PlasticSCM.Editor.ref.dll_257AEB342BE77856.mvfrm","index":232}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PlasticSCM.Editor.ref.dll_257AEB342BE77856.mvfrm","displayName":"Checking for moved APIs","index":232}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.pdb","index":348}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.pdb","displayName":"Copying Unity.PlasticSCM.Editor.pdb","index":348}
{"msg":"noderesult","processed_node_count":322,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.pdb","index":348,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.PlasticSCM.Editor.pdb"}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.dll","index":347}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.dll","displayName":"Copying Unity.PlasticSCM.Editor.dll","index":347}
{"msg":"noderesult","processed_node_count":324,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.dll","index":347,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.PlasticSCM.Editor.dll"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Editor.ref.dll_EB1F4B6E56116D4A.mvfrm","index":318}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Editor.ref.dll_EB1F4B6E56116D4A.mvfrm","displayName":"Checking for moved APIs","index":318}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.dll","index":363}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.dll","displayName":"Copying Unity.Multiplayer.Center.Editor.dll","index":363}
{"msg":"noderesult","processed_node_count":325,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.dll","index":363,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.Multiplayer.Center.Editor.dll"}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.pdb","index":364}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.pdb","displayName":"Copying Unity.Multiplayer.Center.Editor.pdb","index":364}
{"msg":"noderesult","processed_node_count":326,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.pdb","index":364,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.Multiplayer.Center.Editor.pdb"}
{"msg":"noderesult","processed_node_count":327,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.Editor.ref.dll_159E061D77A10B86.mvfrm","index":322,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\Unity.TextMeshPro.Editor.ref.dll_159E061D77A10B86.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.ref.dll_D75DF4EA1AFE54F4.mvfrm","index":269}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.ref.dll_D75DF4EA1AFE54F4.mvfrm","displayName":"Checking for moved APIs","index":269}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.pdb","index":360}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.pdb","displayName":"Copying Unity.VisualScripting.Core.pdb","index":360}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.dll","index":359}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.dll","displayName":"Copying Unity.VisualScripting.Core.dll","index":359}
{"msg":"noderesult","processed_node_count":329,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.pdb","index":360,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.VisualScripting.Core.pdb"}
{"msg":"noderesult","processed_node_count":330,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.dll","index":359,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.VisualScripting.Core.dll"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Performance.Profile-Analyzer.Editor.ref.dll_65A192712782EB2B.mvfrm","index":319}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Performance.Profile-Analyzer.Editor.ref.dll_65A192712782EB2B.mvfrm","displayName":"Checking for moved APIs","index":319}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.pdb","index":346}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.pdb","displayName":"Copying Unity.Performance.Profile-Analyzer.Editor.pdb","index":346}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.dll","index":345}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.dll","displayName":"Copying Unity.Performance.Profile-Analyzer.Editor.dll","index":345}
{"msg":"noderesult","processed_node_count":332,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.pdb","index":346,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.Performance.Profile-Analyzer.Editor.pdb"}
{"msg":"noderesult","processed_node_count":333,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.dll","index":345,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.Performance.Profile-Analyzer.Editor.dll"}
{"msg":"noderesult","processed_node_count":334,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.ref.dll_D75DF4EA1AFE54F4.mvfrm","index":269,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\Unity.VisualScripting.Core.ref.dll_D75DF4EA1AFE54F4.mvfrm"}
{"msg":"noderesult","processed_node_count":335,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Editor.ref.dll_EB1F4B6E56116D4A.mvfrm","index":318,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\Unity.Multiplayer.Center.Editor.ref.dll_EB1F4B6E56116D4A.mvfrm"}
{"msg":"noderesult","processed_node_count":336,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PlasticSCM.Editor.ref.dll_257AEB342BE77856.mvfrm","index":232,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\Unity.PlasticSCM.Editor.ref.dll_257AEB342BE77856.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm","index":277}
{"msg":"runNodeAction","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm","displayName":"Checking for moved APIs","index":277}
{"msg":"newNode","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm","index":271}
{"msg":"runNodeAction","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm","displayName":"Checking for moved APIs","index":271}
{"msg":"newNode","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm","index":234}
{"msg":"runNodeAction","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm","displayName":"Checking for moved APIs","index":234}
{"msg":"noderesult","processed_node_count":337,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Performance.Profile-Analyzer.Editor.ref.dll_65A192712782EB2B.mvfrm","index":319,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\Unity.Performance.Profile-Analyzer.Editor.ref.dll_65A192712782EB2B.mvfrm"}
{"msg":"noderesult","processed_node_count":338,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm","index":234,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.CollabProxy.Editor.dll.mvfrm"}
{"msg":"newNode","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)","index":231}
{"msg":"noderesult","processed_node_count":339,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm","index":277,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Flow.dll.mvfrm"}
{"msg":"noderesult","processed_node_count":340,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm","index":271,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Core.Editor.dll.mvfrm"}
{"msg":"cachemiss","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)","index":231,"inputSignature":"dd3859fab4a68fa7edae7b6a2abd045b"}
{"msg":"runNodeAction","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)","displayName":"Compiling C# (Unity.CollabProxy.Editor)","index":231}
{"msg":"noderesult","processed_node_count":341,"number_of_nodes_ever_queued":386,"annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)","index":231,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.CollabProxy.Editor.dll"}
{"msg":"noderesult","processed_node_count":341,"number_of_nodes_ever_queued":386,"annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)","index":261,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Timeline.Editor.dll"}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.dll","index":361}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.pdb","index":362}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.dll","displayName":"Copying Unity.CollabProxy.Editor.dll","index":361}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.pdb","displayName":"Copying Unity.CollabProxy.Editor.pdb","index":362}
{"msg":"noderesult","processed_node_count":342,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.dll","index":361,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.CollabProxy.Editor.dll"}
{"msg":"noderesult","processed_node_count":343,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.pdb","index":362,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.CollabProxy.Editor.pdb"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.Editor.ref.dll_0D3D3C0B73557612.mvfrm","index":323}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.Editor.ref.dll_0D3D3C0B73557612.mvfrm","displayName":"Checking for moved APIs","index":323}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.pdb","index":370}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.pdb","displayName":"Copying Unity.Timeline.Editor.pdb","index":370}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.dll","index":369}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.dll","displayName":"Copying Unity.Timeline.Editor.dll","index":369}
{"msg":"noderesult","processed_node_count":345,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.pdb","index":370,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.Timeline.Editor.pdb"}
{"msg":"noderesult","processed_node_count":346,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.dll","index":369,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.Timeline.Editor.dll"}
{"msg":"noderesult","processed_node_count":347,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.Editor.ref.dll_0D3D3C0B73557612.mvfrm","index":323,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\Unity.Timeline.Editor.ref.dll_0D3D3C0B73557612.mvfrm"}
{"msg":"newNode","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)","index":275}
{"msg":"cachemiss","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)","index":275,"inputSignature":"01c29f6cae1f0056600007525313dda1"}
{"msg":"runNodeAction","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)","displayName":"Compiling C# (Unity.VisualScripting.Flow)","index":275}
{"msg":"noderesult","processed_node_count":348,"number_of_nodes_ever_queued":386,"annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)","index":275,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Flow.dll"}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.pdb","index":374}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.ref.dll_1E6ED5409D07C9A3.mvfrm","index":283}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.pdb","displayName":"Copying Unity.VisualScripting.Flow.pdb","index":374}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.ref.dll_1E6ED5409D07C9A3.mvfrm","displayName":"Checking for moved APIs","index":283}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.dll","index":373}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.dll","displayName":"Copying Unity.VisualScripting.Flow.dll","index":373}
{"msg":"noderesult","processed_node_count":349,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.pdb","index":374,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.VisualScripting.Flow.pdb"}
{"msg":"noderesult","processed_node_count":350,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.dll","index":373,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.VisualScripting.Flow.dll"}
{"msg":"noderesult","processed_node_count":351,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.ref.dll_1E6ED5409D07C9A3.mvfrm","index":283,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\Unity.VisualScripting.Flow.ref.dll_1E6ED5409D07C9A3.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm","index":291}
{"msg":"runNodeAction","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm","displayName":"Checking for moved APIs","index":291}
{"msg":"noderesult","processed_node_count":352,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm","index":291,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.State.dll.mvfrm"}
{"msg":"newNode","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)","index":289}
{"msg":"cachemiss","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)","index":289,"inputSignature":"eb9b48dccebf6004cac331eea52ece8a"}
{"msg":"runNodeAction","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)","displayName":"Compiling C# (Unity.VisualScripting.State)","index":289}
{"msg":"noderesult","processed_node_count":353,"number_of_nodes_ever_queued":386,"annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)","index":289,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.State.dll"}
{"msg":"newNode","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)","index":268}
{"msg":"cachemiss","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)","index":268,"inputSignature":"24621ea08c5f6d51017efdaf5afb57ca"}
{"msg":"runNodeAction","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)","displayName":"Compiling C# (Unity.VisualScripting.Core.Editor)","index":268}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.ref.dll_D91762B7076BE462.mvfrm","index":297}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.ref.dll_D91762B7076BE462.mvfrm","displayName":"Checking for moved APIs","index":297}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.pdb","index":378}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.pdb","displayName":"Copying Unity.VisualScripting.State.pdb","index":378}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.dll","index":377}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.dll","displayName":"Copying Unity.VisualScripting.State.dll","index":377}
{"msg":"noderesult","processed_node_count":354,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.dll","index":377,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.VisualScripting.State.dll"}
{"msg":"noderesult","processed_node_count":355,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.pdb","index":378,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.VisualScripting.State.pdb"}
{"msg":"noderesult","processed_node_count":356,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.ref.dll_D91762B7076BE462.mvfrm","index":297,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\Unity.VisualScripting.State.ref.dll_D91762B7076BE462.mvfrm"}
{"msg":"noderesult","processed_node_count":357,"number_of_nodes_ever_queued":386,"annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)","index":268,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Core.Editor.dll"}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.pdb","index":372}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.pdb","displayName":"Copying Unity.VisualScripting.Core.Editor.pdb","index":372}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.Editor.ref.dll_EDC8690F57C5BDFD.mvfrm","index":282}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.Editor.ref.dll_EDC8690F57C5BDFD.mvfrm","displayName":"Checking for moved APIs","index":282}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll","index":371}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll","displayName":"Copying Unity.VisualScripting.Core.Editor.dll","index":371}
{"msg":"noderesult","processed_node_count":358,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.pdb","index":372,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.VisualScripting.Core.Editor.pdb"}
{"msg":"noderesult","processed_node_count":359,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll","index":371,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.VisualScripting.Core.Editor.dll"}
{"msg":"noderesult","processed_node_count":360,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.Editor.ref.dll_EDC8690F57C5BDFD.mvfrm","index":282,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\Unity.VisualScripting.Core.Editor.ref.dll_EDC8690F57C5BDFD.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm","index":285}
{"msg":"runNodeAction","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm","displayName":"Checking for moved APIs","index":285}
{"msg":"noderesult","processed_node_count":361,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm","index":285,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Flow.Editor.dll.mvfrm"}
{"msg":"newNode","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)","index":281}
{"msg":"cachemiss","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)","index":281,"inputSignature":"806435092a225b19313fd622b88ef411"}
{"msg":"runNodeAction","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)","displayName":"Compiling C# (Unity.VisualScripting.Flow.Editor)","index":281}
{"msg":"noderesult","processed_node_count":362,"number_of_nodes_ever_queued":386,"annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)","index":281,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Flow.Editor.dll"}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.Editor.ref.dll_6AF951BE66A4493E.mvfrm","index":296}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.Editor.ref.dll_6AF951BE66A4493E.mvfrm","displayName":"Checking for moved APIs","index":296}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.pdb","index":376}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.pdb","displayName":"Copying Unity.VisualScripting.Flow.Editor.pdb","index":376}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.dll","index":375}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.dll","displayName":"Copying Unity.VisualScripting.Flow.Editor.dll","index":375}
{"msg":"noderesult","processed_node_count":363,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.pdb","index":376,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.VisualScripting.Flow.Editor.pdb"}
{"msg":"noderesult","processed_node_count":364,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.dll","index":375,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.VisualScripting.Flow.Editor.dll"}
{"msg":"noderesult","processed_node_count":365,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.Editor.ref.dll_6AF951BE66A4493E.mvfrm","index":296,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\Unity.VisualScripting.Flow.Editor.ref.dll_6AF951BE66A4493E.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm","index":299}
{"msg":"runNodeAction","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm","displayName":"Checking for moved APIs","index":299}
{"msg":"newNode","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm","index":305}
{"msg":"runNodeAction","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm","displayName":"Checking for moved APIs","index":305}
{"msg":"noderesult","processed_node_count":366,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm","index":305,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.State.Editor.dll.mvfrm"}
{"msg":"noderesult","processed_node_count":367,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm","index":299,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}
{"msg":"newNode","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)","index":295}
{"msg":"cachemiss","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)","index":295,"inputSignature":"fd20602773eb2bf7ac9441c240de0fba"}
{"msg":"runNodeAction","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)","displayName":"Compiling C# (Unity.VisualScripting.SettingsProvider.Editor)","index":295}
{"msg":"newNode","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)","index":303}
{"msg":"cachemiss","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)","index":303,"inputSignature":"400d9a17ed7cab7e763ce7f73244a961"}
{"msg":"runNodeAction","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)","displayName":"Compiling C# (Unity.VisualScripting.State.Editor)","index":303}
{"msg":"noderesult","processed_node_count":368,"number_of_nodes_ever_queued":386,"annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)","index":295,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.SettingsProvider.Editor.dll"}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll","index":379}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll","displayName":"Copying Unity.VisualScripting.SettingsProvider.Editor.dll","index":379}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.pdb","index":380}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.pdb","displayName":"Copying Unity.VisualScripting.SettingsProvider.Editor.pdb","index":380}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.SettingsProvider.Editor.ref.dll_A1A44354E0B583A8.mvfrm","index":324}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.SettingsProvider.Editor.ref.dll_A1A44354E0B583A8.mvfrm","displayName":"Checking for moved APIs","index":324}
{"msg":"noderesult","processed_node_count":369,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll","index":379,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.VisualScripting.SettingsProvider.Editor.dll"}
{"msg":"noderesult","processed_node_count":369,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.pdb","index":380,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.VisualScripting.SettingsProvider.Editor.pdb"}
{"msg":"noderesult","processed_node_count":371,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.SettingsProvider.Editor.ref.dll_A1A44354E0B583A8.mvfrm","index":324,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\Unity.VisualScripting.SettingsProvider.Editor.ref.dll_A1A44354E0B583A8.mvfrm"}
{"msg":"noderesult","processed_node_count":372,"number_of_nodes_ever_queued":386,"annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)","index":303,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.State.Editor.dll"}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.pdb","index":382}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.pdb","displayName":"Copying Unity.VisualScripting.State.Editor.pdb","index":382}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.Editor.ref.dll_E03858E727F23F6F.mvfrm","index":310}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.Editor.ref.dll_E03858E727F23F6F.mvfrm","displayName":"Checking for moved APIs","index":310}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.dll","index":381}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.dll","displayName":"Copying Unity.VisualScripting.State.Editor.dll","index":381}
{"msg":"noderesult","processed_node_count":373,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.dll","index":381,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.VisualScripting.State.Editor.dll"}
{"msg":"noderesult","processed_node_count":373,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.pdb","index":382,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.VisualScripting.State.Editor.pdb"}
{"msg":"noderesult","processed_node_count":375,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.Editor.ref.dll_E03858E727F23F6F.mvfrm","index":310,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\Unity.VisualScripting.State.Editor.ref.dll_E03858E727F23F6F.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm","index":312}
{"msg":"runNodeAction","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm","displayName":"Checking for moved APIs","index":312}
{"msg":"noderesult","processed_node_count":376,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm","index":312,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Shared.Editor.dll.mvfrm"}
{"msg":"newNode","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)","index":309}
{"msg":"cachemiss","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)","index":309,"inputSignature":"2f6ad8f6b35ae0e6730363f7ec47119f"}
{"msg":"runNodeAction","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)","displayName":"Compiling C# (Unity.VisualScripting.Shared.Editor)","index":309}
{"msg":"noderesult","processed_node_count":377,"number_of_nodes_ever_queued":386,"annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)","index":309,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Shared.Editor.dll"}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.pdb","index":384}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.pdb","displayName":"Copying Unity.VisualScripting.Shared.Editor.pdb","index":384}
{"msg":"newNode","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Shared.Editor.ref.dll_9669F6EBFF6DFFB7.mvfrm","index":325}
{"msg":"runNodeAction","annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Shared.Editor.ref.dll_9669F6EBFF6DFFB7.mvfrm","displayName":"Checking for moved APIs","index":325}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.dll","index":383}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.dll","displayName":"Copying Unity.VisualScripting.Shared.Editor.dll","index":383}
{"msg":"noderesult","processed_node_count":378,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.pdb","index":384,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.VisualScripting.Shared.Editor.pdb"}
{"msg":"noderesult","processed_node_count":379,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.dll","index":383,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Unity.VisualScripting.Shared.Editor.dll"}
{"msg":"noderesult","processed_node_count":380,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Shared.Editor.ref.dll_9669F6EBFF6DFFB7.mvfrm","index":325,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\mvdfrm\\Unity.VisualScripting.Shared.Editor.ref.dll_9669F6EBFF6DFFB7.mvfrm"}
{"msg":"newNode","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm","index":328}
{"msg":"runNodeAction","annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm","displayName":"Checking for moved APIs","index":328}
{"msg":"noderesult","processed_node_count":381,"number_of_nodes_ever_queued":386,"annotation":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm","index":328,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll.mvfrm"}
{"msg":"newNode","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)","index":316}
{"msg":"runNodeAction","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)","displayName":"Compiling C# (Assembly-CSharp)","index":316}
{"msg":"noderesult","processed_node_count":382,"number_of_nodes_ever_queued":386,"annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)","index":316,"exitcode":0,"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll","index":385}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll","displayName":"Copying Assembly-CSharp.dll","index":385}
{"msg":"newNode","annotation":"CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb","index":386}
{"msg":"runNodeAction","annotation":"CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb","displayName":"Copying Assembly-CSharp.pdb","index":386}
{"msg":"noderesult","processed_node_count":383,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll","index":385,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Assembly-CSharp.dll"}
{"msg":"noderesult","processed_node_count":384,"number_of_nodes_ever_queued":386,"annotation":"CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb","index":386,"exitcode":0,"outputfile":"Library\\ScriptAssemblies\\Assembly-CSharp.pdb"}
{"msg":"newNode","annotation":"ScriptAssemblies","index":6}
{"msg":"runNodeAction","annotation":"ScriptAssemblies","index":6}
