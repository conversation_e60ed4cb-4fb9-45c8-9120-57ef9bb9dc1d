"""
Advanced Export System
Supports multiple formats including BVH, FBX, and custom motion data formats
"""

import json
import numpy as np
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
import xml.etree.ElementTree as ET
from datetime import datetime
import struct

@dataclass
class MotionFrame:
    """Single frame of motion data"""
    timestamp: float
    pose_landmarks: List[List[float]]
    face_landmarks: Optional[List[List[float]]] = None
    left_hand_landmarks: Optional[List[List[float]]] = None
    right_hand_landmarks: Optional[List[List[float]]] = None

@dataclass
class SkeletonDefinition:
    """Skeleton structure definition"""
    joints: List[str]
    hierarchy: Dict[str, str]  # child -> parent mapping
    joint_offsets: Dict[str, List[float]]  # joint -> [x, y, z] offset

class BVHExporter:
    """Export motion data to BVH format"""
    
    # MediaPipe to BVH joint mapping
    MEDIAPIPE_TO_BVH = {
        # Pose landmarks to BVH joints
        23: "Hips",           # Left hip
        24: "RightHip",       # Right hip  
        11: "Spine",          # Left shoulder (approximation)
        12: "RightShoulder",  # Right shoulder
        13: "LeftShoulder",   # Left elbow (approximation)
        14: "RightElbow",     # Right elbow
        15: "LeftElbow",      # Left wrist (approximation)
        16: "RightWrist",     # Right wrist
        0:  "Head",           # Nose (head approximation)
        25: "LeftKnee",       # Left knee
        26: "RightKnee",      # Right knee
        27: "LeftAnkle",      # Left ankle
        28: "RightAnkle",     # Right ankle
    }
    
    def __init__(self):
        self.skeleton = self._create_skeleton_definition()
        self.frame_rate = 30.0
    
    def _create_skeleton_definition(self) -> SkeletonDefinition:
        """Create BVH skeleton definition"""
        joints = [
            "Hips", "Spine", "Chest", "Neck", "Head",
            "LeftShoulder", "LeftElbow", "LeftWrist",
            "RightShoulder", "RightElbow", "RightWrist",
            "LeftHip", "LeftKnee", "LeftAnkle",
            "RightHip", "RightKnee", "RightAnkle"
        ]
        
        hierarchy = {
            "Spine": "Hips",
            "Chest": "Spine",
            "Neck": "Chest",
            "Head": "Neck",
            "LeftShoulder": "Chest",
            "LeftElbow": "LeftShoulder",
            "LeftWrist": "LeftElbow",
            "RightShoulder": "Chest",
            "RightElbow": "RightShoulder",
            "RightWrist": "RightElbow",
            "LeftHip": "Hips",
            "LeftKnee": "LeftHip",
            "LeftAnkle": "LeftKnee",
            "RightHip": "Hips",
            "RightKnee": "RightHip",
            "RightAnkle": "RightKnee"
        }
        
        # Default joint offsets (in cm)
        joint_offsets = {
            "Hips": [0, 0, 0],
            "Spine": [0, 10, 0],
            "Chest": [0, 15, 0],
            "Neck": [0, 20, 0],
            "Head": [0, 10, 0],
            "LeftShoulder": [-15, 0, 0],
            "LeftElbow": [-25, 0, 0],
            "LeftWrist": [-25, 0, 0],
            "RightShoulder": [15, 0, 0],
            "RightElbow": [25, 0, 0],
            "RightWrist": [25, 0, 0],
            "LeftHip": [-10, 0, 0],
            "LeftKnee": [0, -40, 0],
            "LeftAnkle": [0, -40, 0],
            "RightHip": [10, 0, 0],
            "RightKnee": [0, -40, 0],
            "RightAnkle": [0, -40, 0]
        }
        
        return SkeletonDefinition(joints, hierarchy, joint_offsets)
    
    def export_bvh(self, motion_frames: List[MotionFrame], output_path: str) -> bool:
        """Export motion data to BVH format"""
        try:
            with open(output_path, 'w') as f:
                # Write header
                self._write_bvh_header(f)
                
                # Write motion data
                self._write_bvh_motion(f, motion_frames)
            
            print(f"BVH exported successfully to {output_path}")
            return True
            
        except Exception as e:
            print(f"Error exporting BVH: {e}")
            return False
    
    def _write_bvh_header(self, file):
        """Write BVH header section"""
        file.write("HIERARCHY\n")
        file.write("ROOT Hips\n")
        file.write("{\n")
        file.write("    OFFSET 0.0 0.0 0.0\n")
        file.write("    CHANNELS 6 Xposition Yposition Zposition Zrotation Xrotation Yrotation\n")
        
        # Write joint hierarchy
        self._write_joint_hierarchy(file, "Hips", 1)
        
        file.write("}\n")
    
    def _write_joint_hierarchy(self, file, joint_name: str, indent_level: int):
        """Recursively write joint hierarchy"""
        indent = "    " * indent_level
        
        # Find children
        children = [child for child, parent in self.skeleton.hierarchy.items() if parent == joint_name]
        
        for child in children:
            file.write(f"{indent}JOINT {child}\n")
            file.write(f"{indent}{{\n")
            
            offset = self.skeleton.joint_offsets.get(child, [0, 0, 0])
            file.write(f"{indent}    OFFSET {offset[0]:.6f} {offset[1]:.6f} {offset[2]:.6f}\n")
            file.write(f"{indent}    CHANNELS 3 Zrotation Xrotation Yrotation\n")
            
            # Recursively write children
            self._write_joint_hierarchy(file, child, indent_level + 1)
            
            file.write(f"{indent}}}\n")
    
    def _write_bvh_motion(self, file, motion_frames: List[MotionFrame]):
        """Write BVH motion section"""
        file.write("MOTION\n")
        file.write(f"Frames: {len(motion_frames)}\n")
        file.write(f"Frame Time: {1.0/self.frame_rate:.6f}\n")
        
        for frame in motion_frames:
            # Convert pose landmarks to BVH format
            bvh_data = self._convert_frame_to_bvh(frame)
            file.write(" ".join(f"{val:.6f}" for val in bvh_data) + "\n")
    
    def _convert_frame_to_bvh(self, frame: MotionFrame) -> List[float]:
        """Convert motion frame to BVH data"""
        bvh_data = []
        
        # Root position (Hips)
        if len(frame.pose_landmarks) > 23:
            hip_pos = frame.pose_landmarks[23]  # Left hip as reference
            bvh_data.extend([hip_pos[0], hip_pos[1], hip_pos[2]])
        else:
            bvh_data.extend([0.0, 0.0, 0.0])
        
        # Root rotation
        bvh_data.extend([0.0, 0.0, 0.0])
        
        # Joint rotations (simplified - all zeros for now)
        # In a full implementation, you would calculate rotations from landmark positions
        num_joints = len(self.skeleton.joints) - 1  # Exclude root
        for _ in range(num_joints):
            bvh_data.extend([0.0, 0.0, 0.0])  # Zrotation, Xrotation, Yrotation
        
        return bvh_data

class FBXExporter:
    """Export motion data to FBX format (simplified ASCII version)"""
    
    def export_fbx(self, motion_frames: List[MotionFrame], output_path: str) -> bool:
        """Export motion data to simplified FBX format"""
        try:
            with open(output_path, 'w') as f:
                self._write_fbx_header(f)
                self._write_fbx_objects(f)
                self._write_fbx_animation(f, motion_frames)
                self._write_fbx_footer(f)
            
            print(f"FBX exported successfully to {output_path}")
            return True
            
        except Exception as e:
            print(f"Error exporting FBX: {e}")
            return False
    
    def _write_fbx_header(self, file):
        """Write FBX header"""
        file.write("; FBX 7.4.0 project file\n")
        file.write("; Created by Enhanced Motion Capture System\n")
        file.write(f"; Creation time: {datetime.now().isoformat()}\n\n")
        
        file.write("FBXHeaderExtension:  {\n")
        file.write("    FBXHeaderVersion: 1003\n")
        file.write("    FBXVersion: 7400\n")
        file.write("    CreationTimeStamp:  {\n")
        file.write("        Version: 1000\n")
        file.write(f"        Year: {datetime.now().year}\n")
        file.write(f"        Month: {datetime.now().month}\n")
        file.write(f"        Day: {datetime.now().day}\n")
        file.write("    }\n")
        file.write("}\n\n")
    
    def _write_fbx_objects(self, file):
        """Write FBX objects section"""
        file.write("Objects:  {\n")
        file.write("    Geometry: 1234567890, \"Geometry::\", \"Mesh\" {\n")
        file.write("        Properties70:  {\n")
        file.write("        }\n")
        file.write("    }\n")
        file.write("}\n\n")
    
    def _write_fbx_animation(self, file, motion_frames: List[MotionFrame]):
        """Write FBX animation data"""
        file.write("Takes:  {\n")
        file.write("    Current: \"Take 001\"\n")
        file.write("    Take: \"Take 001\" {\n")
        file.write("        FileName: \"Take_001.tak\"\n")
        file.write("        LocalTime: 0,46186158000\n")
        file.write("        ReferenceTime: 0,46186158000\n")
        file.write("    }\n")
        file.write("}\n\n")
    
    def _write_fbx_footer(self, file):
        """Write FBX footer"""
        file.write("; Object connections\n")
        file.write("Connections:  {\n")
        file.write("}\n")

class UnityAnimationExporter:
    """Export motion data for Unity Animation system"""
    
    def export_unity_animation(self, motion_frames: List[MotionFrame], output_path: str) -> bool:
        """Export motion data in Unity-compatible format"""
        try:
            animation_data = {
                "name": "MotionCaptureAnimation",
                "frameRate": 30.0,
                "length": len(motion_frames) / 30.0,
                "frames": []
            }
            
            for i, frame in enumerate(motion_frames):
                frame_data = {
                    "time": i / 30.0,
                    "pose_landmarks": frame.pose_landmarks,
                    "face_landmarks": frame.face_landmarks,
                    "left_hand_landmarks": frame.left_hand_landmarks,
                    "right_hand_landmarks": frame.right_hand_landmarks
                }
                animation_data["frames"].append(frame_data)
            
            with open(output_path, 'w') as f:
                json.dump(animation_data, f, indent=2)
            
            print(f"Unity animation exported to {output_path}")
            return True
            
        except Exception as e:
            print(f"Error exporting Unity animation: {e}")
            return False

class MotionLibrary:
    """Motion data library for organizing and managing captures"""
    
    def __init__(self, library_path: str = "motion_library"):
        self.library_path = Path(library_path)
        self.library_path.mkdir(exist_ok=True)
        
        # Create subdirectories
        (self.library_path / "captures").mkdir(exist_ok=True)
        (self.library_path / "exports").mkdir(exist_ok=True)
        (self.library_path / "metadata").mkdir(exist_ok=True)
        
        self.index_file = self.library_path / "index.json"
        self.index = self._load_index()
    
    def _load_index(self) -> Dict:
        """Load library index"""
        if self.index_file.exists():
            try:
                with open(self.index_file, 'r') as f:
                    return json.load(f)
            except:
                pass
        
        return {"captures": {}, "version": "1.0"}
    
    def _save_index(self):
        """Save library index"""
        with open(self.index_file, 'w') as f:
            json.dump(self.index, f, indent=2)
    
    def add_capture(self, name: str, motion_frames: List[MotionFrame], 
                   metadata: Optional[Dict] = None) -> str:
        """Add motion capture to library"""
        capture_id = f"capture_{len(self.index['captures']):04d}"
        timestamp = datetime.now().isoformat()
        
        # Save motion data
        capture_file = self.library_path / "captures" / f"{capture_id}.json"
        capture_data = {
            "frames": [
                {
                    "timestamp": frame.timestamp,
                    "pose_landmarks": frame.pose_landmarks,
                    "face_landmarks": frame.face_landmarks,
                    "left_hand_landmarks": frame.left_hand_landmarks,
                    "right_hand_landmarks": frame.right_hand_landmarks
                }
                for frame in motion_frames
            ]
        }
        
        with open(capture_file, 'w') as f:
            json.dump(capture_data, f)
        
        # Save metadata
        metadata_file = self.library_path / "metadata" / f"{capture_id}.json"
        full_metadata = {
            "name": name,
            "capture_id": capture_id,
            "created": timestamp,
            "frame_count": len(motion_frames),
            "duration": motion_frames[-1].timestamp - motion_frames[0].timestamp if motion_frames else 0,
            "custom_metadata": metadata or {}
        }
        
        with open(metadata_file, 'w') as f:
            json.dump(full_metadata, f, indent=2)
        
        # Update index
        self.index["captures"][capture_id] = {
            "name": name,
            "created": timestamp,
            "file": str(capture_file),
            "metadata_file": str(metadata_file)
        }
        
        self._save_index()
        
        print(f"Added capture '{name}' to library with ID: {capture_id}")
        return capture_id
    
    def list_captures(self) -> List[Dict]:
        """List all captures in library"""
        captures = []
        
        for capture_id, info in self.index["captures"].items():
            # Load metadata
            metadata_file = Path(info["metadata_file"])
            if metadata_file.exists():
                with open(metadata_file, 'r') as f:
                    metadata = json.load(f)
                captures.append(metadata)
        
        return captures
    
    def export_capture(self, capture_id: str, format: str, output_path: str) -> bool:
        """Export capture in specified format"""
        if capture_id not in self.index["captures"]:
            print(f"Capture {capture_id} not found")
            return False
        
        # Load capture data
        capture_file = Path(self.index["captures"][capture_id]["file"])
        with open(capture_file, 'r') as f:
            capture_data = json.load(f)
        
        # Convert to MotionFrame objects
        motion_frames = []
        for frame_data in capture_data["frames"]:
            frame = MotionFrame(
                timestamp=frame_data["timestamp"],
                pose_landmarks=frame_data["pose_landmarks"],
                face_landmarks=frame_data.get("face_landmarks"),
                left_hand_landmarks=frame_data.get("left_hand_landmarks"),
                right_hand_landmarks=frame_data.get("right_hand_landmarks")
            )
            motion_frames.append(frame)
        
        # Export in requested format
        if format.lower() == "bvh":
            exporter = BVHExporter()
            return exporter.export_bvh(motion_frames, output_path)
        elif format.lower() == "fbx":
            exporter = FBXExporter()
            return exporter.export_fbx(motion_frames, output_path)
        elif format.lower() == "unity":
            exporter = UnityAnimationExporter()
            return exporter.export_unity_animation(motion_frames, output_path)
        else:
            print(f"Unsupported format: {format}")
            return False

# Example usage
def main():
    """Example usage of export system"""
    # Create sample motion data
    motion_frames = []
    for i in range(100):  # 100 frames
        frame = MotionFrame(
            timestamp=i / 30.0,
            pose_landmarks=[[j*10 + i, j*5, j*2, 0.9] for j in range(33)]  # Sample data
        )
        motion_frames.append(frame)
    
    # Initialize motion library
    library = MotionLibrary()
    
    # Add capture to library
    capture_id = library.add_capture(
        "Sample Motion", 
        motion_frames,
        {"description": "Sample motion capture data", "actor": "Test Subject"}
    )
    
    # Export in different formats
    exports_dir = Path("exports")
    exports_dir.mkdir(exist_ok=True)
    
    # Export as BVH
    library.export_capture(capture_id, "bvh", "exports/sample_motion.bvh")
    
    # Export as FBX
    library.export_capture(capture_id, "fbx", "exports/sample_motion.fbx")
    
    # Export for Unity
    library.export_capture(capture_id, "unity", "exports/sample_motion_unity.json")
    
    # List all captures
    captures = library.list_captures()
    print(f"Library contains {len(captures)} captures:")
    for capture in captures:
        print(f"  - {capture['name']} ({capture['frame_count']} frames)")

if __name__ == "__main__":
    main()
