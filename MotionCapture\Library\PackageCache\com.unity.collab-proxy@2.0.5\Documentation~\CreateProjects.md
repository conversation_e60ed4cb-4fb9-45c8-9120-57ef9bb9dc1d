# Create projects

To create projects:

1. In Unity, open the version control window and click on **Create Workspace**.
It will suggest names for your repository (shared files and history) and workspace (your local copy).

If you wish to use an existing version control repository, click the three dots next to the repository name, and select a repository from the list.

2. Select the type of workspace that fits your needs.

* **local workspace**
With this workspace, you can work with branching and merging.

* **Gluon workspace**
This workspace tailored for artists allows you to pick the files you want to work on and check them back in without updating your whole workspace.

1. Add asset files associated with your project.
version control will display the project files from the asset folder in the **Pending changes** tab. You can choose specific files to include or add all to the repository by selecting the files and clicking Checkin changes.

version control will automatically perform a check in for appropriate folders and files – such as package files and project settings – when it’s set up from the Unity Editor. You can view these in the **Changesets tab.**

Once your initial asset check in is complete, you’re set up with version control for Unity and ready to create.
