# Glossary

## General terms

#### Ignore file

A special file used in many **Version Control** Systems which specifies files to be excluded from **version control**. In Unity projects, several files can be excluded from **version control**. Using an Ignore File is the best way to achieve this. See [Using external version control systems with Unity](https://docs.unity3d.com/Manual/ExternalVersionControlSystemSupport.html).

#### Project

In Unity, you use a project to design and develop a game. A project stores all of the files related to a game, such as the asset and **Scene** files. See [2D or 3D projects](https://docs.unity3d.com/Manual/2Dor3D.html).

#### Version Control

A system for managing file changes. You can use Unity in conjunction with most **version control** tools, including **Perforce** , Git, Mercurial, and perforce. See [Version Control](https://docs.unity3d.com/Manual/VersionControl.html).

## Unity version control terms

#### Checkin

Checkin is the act of submitting changes to the repo. You must enter a comment in the text box before you can check in your changes.

#### Developer Workflow

Developers have access to the branch explorer directly from inside Unity and easily switch branches.

#### Gluon Workflow

Artists can take advantage of the Gluon visualized interface and workflow from inside Unity.

#### Organization

The organization handles different sets of repositories in the Cloud. Inside the organization, you can create as many repositories as you need.

#### Workspace

Your workspace interacts with the version control, where you download the files and make the required changes for each checkin.
