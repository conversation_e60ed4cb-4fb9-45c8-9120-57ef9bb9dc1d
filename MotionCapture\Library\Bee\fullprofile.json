{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 31244, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 31244, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 31244, "tid": 18, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 31244, "tid": 18, "ts": 1751984051100708, "dur": 1182, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 31244, "tid": 18, "ts": 1751984051108417, "dur": 1152, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 31244, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 31244, "tid": 21474836480, "ts": 1751984050704928, "dur": 44729, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": 1751984050749659, "dur": 340460, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": 1751984050749679, "dur": 73, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": 1751984050749756, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": 1751984050749759, "dur": 52593, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********02379, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********02387, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********02457, "dur": 17, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********02476, "dur": 2233, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********04719, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********04723, "dur": 141, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********04873, "dur": 6, "ph": "X", "name": "ProcessMessages 1885", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********04883, "dur": 108, "ph": "X", "name": "ReadAsync 1885", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********04995, "dur": 2, "ph": "X", "name": "ProcessMessages 1219", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********04999, "dur": 62, "ph": "X", "name": "ReadAsync 1219", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********05066, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********05069, "dur": 67, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********05139, "dur": 1, "ph": "X", "name": "ProcessMessages 795", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********05142, "dur": 41, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********05187, "dur": 72, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********05264, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********05266, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********05320, "dur": 1, "ph": "X", "name": "ProcessMessages 882", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********05322, "dur": 41, "ph": "X", "name": "ReadAsync 882", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********05367, "dur": 44, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********05413, "dur": 1, "ph": "X", "name": "ProcessMessages 774", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********05415, "dur": 114, "ph": "X", "name": "ReadAsync 774", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********05534, "dur": 1, "ph": "X", "name": "ProcessMessages 758", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********05537, "dur": 65, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********05606, "dur": 6, "ph": "X", "name": "ProcessMessages 2142", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********05613, "dur": 56, "ph": "X", "name": "ReadAsync 2142", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********05671, "dur": 1, "ph": "X", "name": "ProcessMessages 1171", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********05672, "dur": 29, "ph": "X", "name": "ReadAsync 1171", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********05703, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********05706, "dur": 167, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********05878, "dur": 2, "ph": "X", "name": "ProcessMessages 904", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********05881, "dur": 62, "ph": "X", "name": "ReadAsync 904", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********05945, "dur": 2, "ph": "X", "name": "ProcessMessages 2054", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********05947, "dur": 125, "ph": "X", "name": "ReadAsync 2054", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********06077, "dur": 136, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********06218, "dur": 2, "ph": "X", "name": "ProcessMessages 1756", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********06220, "dur": 148, "ph": "X", "name": "ReadAsync 1756", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********06372, "dur": 1, "ph": "X", "name": "ProcessMessages 1208", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********06375, "dur": 74, "ph": "X", "name": "ReadAsync 1208", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********06452, "dur": 2, "ph": "X", "name": "ProcessMessages 2680", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********06456, "dur": 123, "ph": "X", "name": "ReadAsync 2680", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********06584, "dur": 1, "ph": "X", "name": "ProcessMessages 1202", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********06586, "dur": 72, "ph": "X", "name": "ReadAsync 1202", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********06661, "dur": 2, "ph": "X", "name": "ProcessMessages 2965", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********06664, "dur": 127, "ph": "X", "name": "ReadAsync 2965", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********06796, "dur": 72, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********06871, "dur": 2, "ph": "X", "name": "ProcessMessages 2858", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********06874, "dur": 36, "ph": "X", "name": "ReadAsync 2858", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********06913, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********06954, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********06956, "dur": 41, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********07000, "dur": 24, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********07026, "dur": 1, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********07028, "dur": 119, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********07150, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********07153, "dur": 197, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********07354, "dur": 2, "ph": "X", "name": "ProcessMessages 3010", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********07358, "dur": 132, "ph": "X", "name": "ReadAsync 3010", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********07494, "dur": 2, "ph": "X", "name": "ProcessMessages 2466", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********07499, "dur": 126, "ph": "X", "name": "ReadAsync 2466", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********07629, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********07631, "dur": 75, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********07708, "dur": 2, "ph": "X", "name": "ProcessMessages 3062", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********07712, "dur": 133, "ph": "X", "name": "ReadAsync 3062", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********07849, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********07851, "dur": 146, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********08001, "dur": 2, "ph": "X", "name": "ProcessMessages 1391", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********08004, "dur": 69, "ph": "X", "name": "ReadAsync 1391", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********08076, "dur": 3, "ph": "X", "name": "ProcessMessages 2305", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********08080, "dur": 40, "ph": "X", "name": "ReadAsync 2305", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********08124, "dur": 84, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********08212, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********08214, "dur": 32, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********08251, "dur": 30, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********08283, "dur": 2, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********08285, "dur": 24, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********08311, "dur": 21, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********08336, "dur": 85, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********08426, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********08473, "dur": 1, "ph": "X", "name": "ProcessMessages 1549", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********08475, "dur": 100, "ph": "X", "name": "ReadAsync 1549", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********08580, "dur": 38, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********08620, "dur": 1, "ph": "X", "name": "ProcessMessages 811", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********08621, "dur": 38, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********08662, "dur": 101, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********08766, "dur": 32, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********08800, "dur": 1, "ph": "X", "name": "ProcessMessages 888", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********08801, "dur": 29, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********08833, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********08835, "dur": 38, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********08875, "dur": 1, "ph": "X", "name": "ProcessMessages 914", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********08876, "dur": 24, "ph": "X", "name": "ReadAsync 914", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********08903, "dur": 29, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********08934, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********08936, "dur": 31, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********08970, "dur": 27, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********09000, "dur": 25, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********09027, "dur": 25, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********09054, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********09056, "dur": 24, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********09084, "dur": 28, "ph": "X", "name": "ReadAsync 119", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********09117, "dur": 24, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********09143, "dur": 26, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********09172, "dur": 24, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********09199, "dur": 25, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********09226, "dur": 20, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********09248, "dur": 27, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********09277, "dur": 23, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********09303, "dur": 31, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********09338, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********09341, "dur": 41, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********09386, "dur": 21, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********09409, "dur": 1, "ph": "X", "name": "ProcessMessages 88", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********09412, "dur": 22, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********09436, "dur": 27, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********09466, "dur": 25, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********09493, "dur": 27, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********09523, "dur": 26, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********09552, "dur": 32, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********09586, "dur": 1, "ph": "X", "name": "ProcessMessages 227", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********09588, "dur": 44, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********09634, "dur": 1, "ph": "X", "name": "ProcessMessages 866", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********09635, "dur": 22, "ph": "X", "name": "ReadAsync 866", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********09660, "dur": 27, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********09689, "dur": 25, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********09717, "dur": 23, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********09744, "dur": 38, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********09785, "dur": 29, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********09817, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********09820, "dur": 31, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********09853, "dur": 29, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********09886, "dur": 22, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********09911, "dur": 111, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********10025, "dur": 61, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********10089, "dur": 2, "ph": "X", "name": "ProcessMessages 1596", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********10093, "dur": 83, "ph": "X", "name": "ReadAsync 1596", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********10178, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********10180, "dur": 44, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********10227, "dur": 1, "ph": "X", "name": "ProcessMessages 775", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********10229, "dur": 64, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********10298, "dur": 32, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********10331, "dur": 1, "ph": "X", "name": "ProcessMessages 667", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********10333, "dur": 35, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********10371, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********10373, "dur": 33, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********10408, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********10410, "dur": 198, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********10611, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********10645, "dur": 1, "ph": "X", "name": "ProcessMessages 720", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********10647, "dur": 25, "ph": "X", "name": "ReadAsync 720", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********10675, "dur": 33, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********10710, "dur": 30, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********10744, "dur": 21, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********10768, "dur": 28, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********10800, "dur": 25, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********10828, "dur": 34, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********10863, "dur": 1, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********10865, "dur": 27, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********10895, "dur": 21, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********10919, "dur": 27, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********10948, "dur": 25, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********10976, "dur": 28, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********11007, "dur": 24, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********11034, "dur": 29, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********11065, "dur": 30, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********11099, "dur": 30, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********11131, "dur": 26, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********11160, "dur": 37, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********11200, "dur": 2, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********11203, "dur": 50, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********11256, "dur": 28, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********11287, "dur": 31, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********11321, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********11323, "dur": 27, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********11352, "dur": 23, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********11378, "dur": 91, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********11473, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********11475, "dur": 55, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********11532, "dur": 1, "ph": "X", "name": "ProcessMessages 1772", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********11534, "dur": 39, "ph": "X", "name": "ReadAsync 1772", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********11575, "dur": 1, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********11578, "dur": 48, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********11629, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********11633, "dur": 34, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********11671, "dur": 2, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********11675, "dur": 38, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********11716, "dur": 1, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********11719, "dur": 50, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********11771, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********11774, "dur": 34, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********11810, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********11813, "dur": 36, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********11850, "dur": 3, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********11855, "dur": 26, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********11883, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********11885, "dur": 53, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********11940, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********11942, "dur": 39, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********11982, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********11987, "dur": 34, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********12024, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********12026, "dur": 35, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********12063, "dur": 1, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********12065, "dur": 32, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********12099, "dur": 1, "ph": "X", "name": "ProcessMessages 398", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********12100, "dur": 24, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********12126, "dur": 3, "ph": "X", "name": "ProcessMessages 93", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********12131, "dur": 44, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********12177, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********12179, "dur": 28, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********12210, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********12253, "dur": 37, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********12294, "dur": 1, "ph": "X", "name": "ProcessMessages 386", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********12296, "dur": 45, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********12343, "dur": 1, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********12345, "dur": 53, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********12402, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********12431, "dur": 32, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********12465, "dur": 25, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********12493, "dur": 31, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********12526, "dur": 1, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********12528, "dur": 32, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********12561, "dur": 2, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********12564, "dur": 23, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********12590, "dur": 33, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********12626, "dur": 30, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********12658, "dur": 1, "ph": "X", "name": "ProcessMessages 710", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********12660, "dur": 65, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********12727, "dur": 1, "ph": "X", "name": "ProcessMessages 1003", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********12730, "dur": 29, "ph": "X", "name": "ReadAsync 1003", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********12761, "dur": 24, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********12788, "dur": 27, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********12818, "dur": 25, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********12846, "dur": 20, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********12869, "dur": 23, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********12894, "dur": 27, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********12924, "dur": 26, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********12952, "dur": 26, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********12981, "dur": 25, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********13008, "dur": 26, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********13037, "dur": 21, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********13060, "dur": 28, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********13089, "dur": 2, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********13093, "dur": 42, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********13136, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********13138, "dur": 100, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********13240, "dur": 1, "ph": "X", "name": "ProcessMessages 1533", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********13242, "dur": 57, "ph": "X", "name": "ReadAsync 1533", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********13301, "dur": 24, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********13328, "dur": 24, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********13355, "dur": 32, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********13389, "dur": 1, "ph": "X", "name": "ProcessMessages 670", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********13390, "dur": 27, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********13420, "dur": 25, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********13448, "dur": 22, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********13472, "dur": 29, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********13504, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********13505, "dur": 32, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********13539, "dur": 1, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********13540, "dur": 29, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********13572, "dur": 25, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********13600, "dur": 22, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********13624, "dur": 28, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********13656, "dur": 41, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********13701, "dur": 2, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********13704, "dur": 47, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********13754, "dur": 3, "ph": "X", "name": "ProcessMessages 800", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********13759, "dur": 62, "ph": "X", "name": "ReadAsync 800", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********13823, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********13826, "dur": 38, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********13865, "dur": 1, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********13867, "dur": 31, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********13902, "dur": 1, "ph": "X", "name": "ProcessMessages 165", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********13904, "dur": 40, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********13946, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********13948, "dur": 52, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********14003, "dur": 1, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********14005, "dur": 35, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********14042, "dur": 1, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********14044, "dur": 36, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********14082, "dur": 1, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********14085, "dur": 24, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********14110, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********14112, "dur": 35, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********14149, "dur": 1, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********14151, "dur": 42, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********14195, "dur": 1, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********14197, "dur": 46, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********14246, "dur": 31, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********14280, "dur": 23, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********14306, "dur": 31, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********14340, "dur": 28, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********14371, "dur": 28, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********14401, "dur": 29, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********14433, "dur": 36, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********14473, "dur": 30, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********14505, "dur": 29, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********14536, "dur": 35, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********14574, "dur": 20, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********14597, "dur": 31, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********14632, "dur": 37, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********14672, "dur": 25, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********14700, "dur": 21, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********14723, "dur": 23, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********14748, "dur": 26, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********14776, "dur": 616, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********15397, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********15400, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********15436, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********15439, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********15474, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********15477, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********15510, "dur": 23, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********15536, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********15538, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********15565, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********15593, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********15627, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********15655, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********15658, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********15684, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********15711, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********15735, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********15737, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********15762, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********15786, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********15812, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********15839, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********15869, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********15893, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********15895, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********15924, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********15949, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********15981, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16011, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16013, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16041, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16068, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16095, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16119, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16145, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16147, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16175, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16177, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16203, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16229, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16257, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16282, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16284, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16312, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16339, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16374, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16399, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16402, "dur": 34, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16440, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16442, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16473, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16502, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16527, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16555, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16581, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16609, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16638, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16665, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16668, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16694, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16737, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16760, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16762, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16793, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16821, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16872, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16875, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16905, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16907, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16935, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16937, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16989, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********16991, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17020, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17022, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17049, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17075, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17102, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17104, "dur": 51, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17158, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17160, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17194, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17197, "dur": 28, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17227, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17231, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17260, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17261, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17291, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17315, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17318, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17341, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17343, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17370, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17403, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17432, "dur": 54, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17490, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17521, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17524, "dur": 28, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17554, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17556, "dur": 22, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17580, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17583, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17616, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17619, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17644, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17677, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17680, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17709, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17711, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17737, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17739, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17765, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17791, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17793, "dur": 135, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17932, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17934, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17971, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********17973, "dur": 28, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18005, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18033, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18061, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18093, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18119, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18146, "dur": 25, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18173, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18175, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18202, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18204, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18230, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18253, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18255, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18281, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18305, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18329, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18358, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18362, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18389, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18391, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18418, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18450, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18452, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18476, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18478, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18509, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18539, "dur": 22, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18564, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18565, "dur": 25, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18594, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18620, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18622, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18650, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18679, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18707, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18731, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18733, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18760, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18786, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18788, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18816, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18818, "dur": 25, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18845, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18847, "dur": 26, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18875, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18878, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18908, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18910, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18936, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18938, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18968, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18970, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18992, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********18994, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********19027, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********19029, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********19062, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********19064, "dur": 25, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********19093, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********19121, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********19147, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********19148, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********19194, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********19196, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********19234, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********19237, "dur": 25, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********19264, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********19266, "dur": 36, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********19305, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********19333, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********19335, "dur": 931, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********20274, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********20277, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********20301, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********20309, "dur": 628, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********20943, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********20945, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********20985, "dur": 45, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********21031, "dur": 7933, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********28976, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********28982, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********29020, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********29022, "dur": 774, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********29803, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********29811, "dur": 83, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********29898, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********29900, "dur": 37, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********29940, "dur": 642, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********30587, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********30589, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********30636, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********30639, "dur": 108, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********30750, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********30752, "dur": 111, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********30867, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********30869, "dur": 269, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********31144, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********31181, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********31184, "dur": 162, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********31350, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********31380, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********31382, "dur": 30, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********31416, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********31447, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********31472, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********31495, "dur": 560, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********32058, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********32082, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********32085, "dur": 21, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********32109, "dur": 106, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********32219, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********32243, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********32297, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********32326, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********32328, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********32352, "dur": 91, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********32447, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********32480, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********32506, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********32508, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********32535, "dur": 151, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********32689, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********32711, "dur": 235, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********32950, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********32974, "dur": 136, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********33114, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********33137, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********33140, "dur": 116, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********33259, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********33284, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********33286, "dur": 30, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********33320, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********33323, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********33358, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********33360, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********33385, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********33417, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********33445, "dur": 22, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********33468, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********33470, "dur": 36, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********33510, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********33535, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********33568, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********33599, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********33631, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********33634, "dur": 26, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********33662, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********33665, "dur": 25, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********33693, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********33695, "dur": 23, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********33720, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********33721, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********33752, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********33754, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********33789, "dur": 87, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********33881, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********33908, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********33943, "dur": 273, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********34221, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********34261, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********34263, "dur": 36, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********34301, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********34303, "dur": 348, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********34655, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********34688, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********34690, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********34726, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********34728, "dur": 65, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********34797, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********34826, "dur": 106, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********34936, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********34966, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********34999, "dur": 78, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********35080, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********35112, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********35114, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********35147, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********35149, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********35185, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********35218, "dur": 463, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********35685, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********35717, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********35719, "dur": 182, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********35906, "dur": 107, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********36017, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********36019, "dur": 453, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********36475, "dur": 148, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********36627, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********36630, "dur": 37, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********36670, "dur": 208, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********36882, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********36885, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********36921, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********36923, "dur": 58, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********36985, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********37017, "dur": 124, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********37145, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********37189, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********37193, "dur": 633, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********37832, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********37878, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": ***********37882, "dur": 220216, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": 1751984051058115, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": 1751984051058123, "dur": 328, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": 1751984051058456, "dur": 39, "ph": "X", "name": "ProcessMessages 888", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": 1751984051058496, "dur": 12057, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": 1751984051070560, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": 1751984051070563, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": 1751984051070593, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 31244, "tid": 21474836480, "ts": 1751984051070595, "dur": 19514, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 31244, "tid": 18, "ts": 1751984051109577, "dur": 1541, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 31244, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 31244, "tid": 17179869184, "ts": 1751984050704876, "dur": 6, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 31244, "tid": 17179869184, "ts": 1751984050704883, "dur": 44844, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 31244, "tid": 17179869184, "ts": 1751984050749728, "dur": 30, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 31244, "tid": 18, "ts": 1751984051111120, "dur": 8, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 31244, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 31244, "tid": 1, "ts": 1751984046043670, "dur": 6075, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 31244, "tid": 1, "ts": 1751984046049751, "dur": 55860, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 31244, "tid": 1, "ts": 1751984046105620, "dur": 1132239, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 31244, "tid": 18, "ts": 1751984051111130, "dur": 6, "ph": "X", "name": "", "args": {}}, {"pid": 31244, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046041315, "dur": 562709, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046604028, "dur": 3511820, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046605357, "dur": 2949, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046608314, "dur": 1521, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046609841, "dur": 65259, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046675116, "dur": 274, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046675394, "dur": 121, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046675519, "dur": 821, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046676344, "dur": 166412, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046842772, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046842777, "dur": 99, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046842879, "dur": 996, "ph": "X", "name": "ProcessMessages 2458", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046843883, "dur": 443, "ph": "X", "name": "ReadAsync 2458", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046844330, "dur": 7, "ph": "X", "name": "ProcessMessages 9390", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046844339, "dur": 49, "ph": "X", "name": "ReadAsync 9390", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046844389, "dur": 1, "ph": "X", "name": "ProcessMessages 1003", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046844390, "dur": 27, "ph": "X", "name": "ReadAsync 1003", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046844420, "dur": 28, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046844451, "dur": 31, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046844484, "dur": 29, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046844516, "dur": 26, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046844545, "dur": 38, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046844588, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046844590, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046844639, "dur": 1, "ph": "X", "name": "ProcessMessages 826", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046844642, "dur": 37, "ph": "X", "name": "ReadAsync 826", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046844681, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046844683, "dur": 35, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046844719, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046844721, "dur": 38, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046844761, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046844763, "dur": 37, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046844802, "dur": 1, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046844804, "dur": 33, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046844841, "dur": 38, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046844882, "dur": 37, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046844921, "dur": 1, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046844923, "dur": 33, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046844958, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046844959, "dur": 119, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046845082, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046845084, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046845150, "dur": 3, "ph": "X", "name": "ProcessMessages 2694", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046845154, "dur": 36, "ph": "X", "name": "ReadAsync 2694", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046845194, "dur": 38, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046845234, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046845236, "dur": 36, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046845274, "dur": 1, "ph": "X", "name": "ProcessMessages 670", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046845276, "dur": 34, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046845313, "dur": 35, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046845350, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046845351, "dur": 35, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046845388, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046845390, "dur": 36, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046845428, "dur": 1, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046845431, "dur": 37, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046845470, "dur": 1, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046845471, "dur": 37, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046845510, "dur": 1, "ph": "X", "name": "ProcessMessages 748", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046845512, "dur": 37, "ph": "X", "name": "ReadAsync 748", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046845551, "dur": 1, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046845553, "dur": 36, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046845591, "dur": 1, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046845592, "dur": 37, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046845631, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046845633, "dur": 30, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046845667, "dur": 38, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046845707, "dur": 1, "ph": "X", "name": "ProcessMessages 856", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046845709, "dur": 36, "ph": "X", "name": "ReadAsync 856", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046845747, "dur": 1, "ph": "X", "name": "ProcessMessages 642", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046845748, "dur": 45, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046845795, "dur": 1, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046845797, "dur": 38, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046845837, "dur": 1, "ph": "X", "name": "ProcessMessages 771", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046845839, "dur": 237, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046846088, "dur": 6, "ph": "X", "name": "ProcessMessages 579", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046846098, "dur": 340, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046846442, "dur": 7, "ph": "X", "name": "ProcessMessages 5505", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046846451, "dur": 71, "ph": "X", "name": "ReadAsync 5505", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046846528, "dur": 3, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046846534, "dur": 111, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046846648, "dur": 1, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046846651, "dur": 74, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046846730, "dur": 5, "ph": "X", "name": "ProcessMessages 1068", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046846739, "dur": 41, "ph": "X", "name": "ReadAsync 1068", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046846783, "dur": 1, "ph": "X", "name": "ProcessMessages 143", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046846787, "dur": 53, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046846848, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046846850, "dur": 142, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046846995, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046846997, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046847076, "dur": 2, "ph": "X", "name": "ProcessMessages 1321", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046847080, "dur": 43, "ph": "X", "name": "ReadAsync 1321", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046847129, "dur": 33, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046847166, "dur": 43, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046847212, "dur": 44, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046847258, "dur": 1, "ph": "X", "name": "ProcessMessages 759", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046847259, "dur": 40, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046847301, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046847303, "dur": 104, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046847412, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046847414, "dur": 138, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046847555, "dur": 2, "ph": "X", "name": "ProcessMessages 2033", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046847558, "dur": 67, "ph": "X", "name": "ReadAsync 2033", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046847627, "dur": 2, "ph": "X", "name": "ProcessMessages 2193", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046847630, "dur": 115, "ph": "X", "name": "ReadAsync 2193", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046847749, "dur": 178, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046847930, "dur": 2, "ph": "X", "name": "ProcessMessages 1698", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046847935, "dur": 51, "ph": "X", "name": "ReadAsync 1698", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046847988, "dur": 1, "ph": "X", "name": "ProcessMessages 1234", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046847990, "dur": 29, "ph": "X", "name": "ReadAsync 1234", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046848020, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046848023, "dur": 104, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046848131, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046848133, "dur": 154, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046848291, "dur": 2, "ph": "X", "name": "ProcessMessages 2217", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046848294, "dur": 158, "ph": "X", "name": "ReadAsync 2217", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046848456, "dur": 2, "ph": "X", "name": "ProcessMessages 3044", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046848461, "dur": 133, "ph": "X", "name": "ReadAsync 3044", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046848597, "dur": 2, "ph": "X", "name": "ProcessMessages 1853", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046848601, "dur": 143, "ph": "X", "name": "ReadAsync 1853", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046848748, "dur": 2, "ph": "X", "name": "ProcessMessages 2390", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046848751, "dur": 39, "ph": "X", "name": "ReadAsync 2390", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046848791, "dur": 1, "ph": "X", "name": "ProcessMessages 958", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046848793, "dur": 28, "ph": "X", "name": "ReadAsync 958", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046848825, "dur": 107, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046848935, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046848937, "dur": 107, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046849048, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046849050, "dur": 642, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046849698, "dur": 2, "ph": "X", "name": "ProcessMessages 1449", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046849701, "dur": 72, "ph": "X", "name": "ReadAsync 1449", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046849778, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046849781, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046849839, "dur": 2, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046849842, "dur": 255, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046850101, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046850154, "dur": 1, "ph": "X", "name": "ProcessMessages 1025", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046850157, "dur": 35, "ph": "X", "name": "ReadAsync 1025", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046850194, "dur": 1, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046850196, "dur": 39, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046850237, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046850239, "dur": 56, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046850300, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046850350, "dur": 1, "ph": "X", "name": "ProcessMessages 1242", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046850353, "dur": 28, "ph": "X", "name": "ReadAsync 1242", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046850386, "dur": 36, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046850424, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046850426, "dur": 44, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046850474, "dur": 32, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046850509, "dur": 22, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046850534, "dur": 79, "ph": "X", "name": "ReadAsync 97", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046850617, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046850656, "dur": 1, "ph": "X", "name": "ProcessMessages 873", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046850658, "dur": 26, "ph": "X", "name": "ReadAsync 873", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046850686, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046850687, "dur": 22, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046850713, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046850749, "dur": 26, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046850778, "dur": 33, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046850812, "dur": 1, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046850814, "dur": 27, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046850845, "dur": 28, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046850875, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046850876, "dur": 22, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046850901, "dur": 27, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046850930, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046850932, "dur": 26, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046850960, "dur": 1, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046850962, "dur": 30, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046850995, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046850998, "dur": 119, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046851121, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046851123, "dur": 52, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046851178, "dur": 1, "ph": "X", "name": "ProcessMessages 1313", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046851180, "dur": 51, "ph": "X", "name": "ReadAsync 1313", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046851234, "dur": 1, "ph": "X", "name": "ProcessMessages 738", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046851236, "dur": 61, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046851300, "dur": 1, "ph": "X", "name": "ProcessMessages 655", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046851303, "dur": 127, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046851434, "dur": 2, "ph": "X", "name": "ProcessMessages 902", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046851438, "dur": 57, "ph": "X", "name": "ReadAsync 902", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046851497, "dur": 1, "ph": "X", "name": "ProcessMessages 704", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046851499, "dur": 124, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046851629, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046851682, "dur": 1, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046851684, "dur": 40, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046851728, "dur": 52, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046851782, "dur": 1, "ph": "X", "name": "ProcessMessages 1028", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046851784, "dur": 64, "ph": "X", "name": "ReadAsync 1028", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046851852, "dur": 59, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046851914, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046851917, "dur": 41, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046851966, "dur": 36, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046852004, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046852006, "dur": 36, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046852043, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046852045, "dur": 33, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046852082, "dur": 36, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046852121, "dur": 31, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046852155, "dur": 27, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046852184, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046852186, "dur": 31, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046852221, "dur": 30, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046852253, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046852255, "dur": 34, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046852292, "dur": 36, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046852330, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046852332, "dur": 36, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046852374, "dur": 27, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046852404, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046852454, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046852456, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046852495, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046852497, "dur": 36, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046852535, "dur": 1, "ph": "X", "name": "ProcessMessages 803", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046852537, "dur": 51, "ph": "X", "name": "ReadAsync 803", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046852591, "dur": 1, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046852594, "dur": 37, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046852633, "dur": 2, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046852637, "dur": 31, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046852672, "dur": 46, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046852724, "dur": 2, "ph": "X", "name": "ProcessMessages 219", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046852730, "dur": 74, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046852807, "dur": 1, "ph": "X", "name": "ProcessMessages 699", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046852810, "dur": 53, "ph": "X", "name": "ReadAsync 699", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046852865, "dur": 2, "ph": "X", "name": "ProcessMessages 730", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046852868, "dur": 46, "ph": "X", "name": "ReadAsync 730", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046852917, "dur": 1, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046852919, "dur": 83, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046853008, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046853071, "dur": 1, "ph": "X", "name": "ProcessMessages 1218", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046853073, "dur": 36, "ph": "X", "name": "ReadAsync 1218", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046853112, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046853114, "dur": 38, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046853156, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046853159, "dur": 37, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046853200, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046853201, "dur": 426, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046853632, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046853635, "dur": 50, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046853687, "dur": 1, "ph": "X", "name": "ProcessMessages 805", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046853689, "dur": 57, "ph": "X", "name": "ReadAsync 805", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046853750, "dur": 1, "ph": "X", "name": "ProcessMessages 127", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046853752, "dur": 42, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046853796, "dur": 1, "ph": "X", "name": "ProcessMessages 726", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046853799, "dur": 33, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046853835, "dur": 26, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046853865, "dur": 31, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046853899, "dur": 29, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046853930, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046853933, "dur": 32, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046853967, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046853969, "dur": 29, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046854001, "dur": 48, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046854053, "dur": 1, "ph": "X", "name": "ProcessMessages 251", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046854055, "dur": 43, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046854100, "dur": 1, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046854103, "dur": 40, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046854144, "dur": 1, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046854146, "dur": 36, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046854186, "dur": 36, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046854224, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046854226, "dur": 28, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046854257, "dur": 43, "ph": "X", "name": "ReadAsync 113", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046854303, "dur": 1, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046854305, "dur": 66, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046854375, "dur": 2, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046854379, "dur": 49, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046854430, "dur": 2, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046854433, "dur": 39, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046854475, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046854478, "dur": 41, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046854521, "dur": 1, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046854523, "dur": 40, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046854566, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046854568, "dur": 35, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046854606, "dur": 1, "ph": "X", "name": "ProcessMessages 72", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046854608, "dur": 41, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046854652, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046854654, "dur": 36, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046854694, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046854696, "dur": 44, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046854742, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046854745, "dur": 46, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046854796, "dur": 2, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046854799, "dur": 42, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046854844, "dur": 1, "ph": "X", "name": "ProcessMessages 175", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046854846, "dur": 50, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046854899, "dur": 1, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046854902, "dur": 55, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046854959, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046854962, "dur": 42, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046855007, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046855010, "dur": 36, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046855048, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046855051, "dur": 39, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046855093, "dur": 1, "ph": "X", "name": "ProcessMessages 273", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046855095, "dur": 46, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046855144, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046855147, "dur": 57, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046855208, "dur": 2, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046855211, "dur": 41, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046855255, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046855258, "dur": 39, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046855300, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046855302, "dur": 65, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046855371, "dur": 1, "ph": "X", "name": "ProcessMessages 125", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046855373, "dur": 78, "ph": "X", "name": "ReadAsync 125", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046855456, "dur": 2, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046855460, "dur": 55, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046855518, "dur": 2, "ph": "X", "name": "ProcessMessages 1026", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046855521, "dur": 61, "ph": "X", "name": "ReadAsync 1026", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046855586, "dur": 1, "ph": "X", "name": "ProcessMessages 275", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046855588, "dur": 44, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046855636, "dur": 40, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046855679, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046855682, "dur": 38, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046855723, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046855727, "dur": 72, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046855802, "dur": 1, "ph": "X", "name": "ProcessMessages 235", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046855805, "dur": 47, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046855855, "dur": 1, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046855858, "dur": 42, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046855904, "dur": 1, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046855907, "dur": 31, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046855940, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046855943, "dur": 54, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046855999, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046856003, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046856041, "dur": 1, "ph": "X", "name": "ProcessMessages 203", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046856044, "dur": 110332, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046966400, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046966410, "dur": 94, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046966508, "dur": 614, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046967130, "dur": 464, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046967602, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046967606, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046967672, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046967677, "dur": 47, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046967727, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046967730, "dur": 82, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046967816, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046967819, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046967874, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046967878, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046967925, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046967927, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046967988, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046967991, "dur": 92, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046968089, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046968092, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046968197, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046968200, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046968256, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046968258, "dur": 80, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046968344, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046968347, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046968411, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046968414, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046968465, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046968467, "dur": 145, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046968616, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046968619, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046968668, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046968672, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046968717, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046968720, "dur": 130, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046968855, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046968858, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046968912, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046968915, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046968979, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046969024, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046969027, "dur": 8646, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046977686, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046977693, "dur": 145, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046977842, "dur": 15, "ph": "X", "name": "ProcessMessages 1188", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046977859, "dur": 49, "ph": "X", "name": "ReadAsync 1188", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046977913, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046977915, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046977955, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046977957, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046977994, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046977996, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046978035, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046978084, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046978126, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046978128, "dur": 45, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046978176, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046978177, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046978215, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046978217, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046978256, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046978258, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046978295, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046978297, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046978333, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046978335, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046978378, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046978380, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046978430, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046978433, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046978476, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046978478, "dur": 81, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046978568, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046978573, "dur": 54, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046978630, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046978633, "dur": 38, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046978675, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046978677, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046978716, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046978720, "dur": 41, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046978764, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046978766, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046978804, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046978806, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046978847, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046978850, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046978886, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046978888, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046978931, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046978933, "dur": 64, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046979001, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046979004, "dur": 51, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046979059, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046979063, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046979132, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046979135, "dur": 40, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046979178, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046979182, "dur": 37, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046979222, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046979225, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046979271, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046979274, "dur": 49, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046979326, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046979329, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046979380, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046979384, "dur": 48, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046979435, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046979437, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046979480, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046979482, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046979532, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046979535, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046979577, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046979579, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046979620, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046979624, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046979661, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046979664, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046979702, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046979705, "dur": 210, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046979920, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046979923, "dur": 57, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046979985, "dur": 4, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046979992, "dur": 57, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046980054, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046980059, "dur": 44, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046980112, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046980116, "dur": 44, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046980163, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046980166, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046980216, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046980219, "dur": 45, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046980268, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046980272, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046980323, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046980327, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046980374, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046980376, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046980422, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046980424, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046980469, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046980471, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046980514, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046980516, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046980564, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046980567, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046980607, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046980609, "dur": 62, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046980677, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046980682, "dur": 47, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046980731, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046980734, "dur": 266, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046981004, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046981006, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046981045, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046981048, "dur": 149, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046981203, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046981247, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984046981251, "dur": 29102, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047010363, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047010367, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047010426, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047010429, "dur": 51, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047010483, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047010514, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047010516, "dur": 141, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047010662, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047010697, "dur": 658, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047011362, "dur": 85, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047011450, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047011451, "dur": 226, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047011682, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047011706, "dur": 91, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047011802, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047011826, "dur": 423, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047012254, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047012256, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047012295, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047012298, "dur": 82, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047012385, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047012413, "dur": 21, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047012437, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047012439, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047012464, "dur": 77, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047012545, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047012566, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047012568, "dur": 443, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047013014, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047013039, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047013061, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047013124, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047013148, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047013197, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047013221, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047013245, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047013267, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047013289, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047013313, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047013383, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047013420, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047013446, "dur": 84, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047013533, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047013563, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047013565, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047013590, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047013614, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047013654, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047013674, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047013709, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047013731, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047013780, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047013801, "dur": 486, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047014290, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047014311, "dur": 79, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047014394, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047014421, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047014423, "dur": 21, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047014447, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047014484, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047014507, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047014563, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047014593, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047014618, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047014646, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047014670, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047014691, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047014717, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047014740, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047014761, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047014782, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047014803, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047014824, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047014858, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047014878, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047014900, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047014921, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047014969, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047014971, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047015001, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047015023, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047015049, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047015073, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047015095, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047015097, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047015121, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047015143, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047015165, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047015189, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047015210, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047015235, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047015255, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047015280, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047015301, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047015322, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047015343, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047015345, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047015379, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047015381, "dur": 54, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047015439, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047015470, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047015516, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047015519, "dur": 101, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047015624, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047015626, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047015662, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047015664, "dur": 525, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047016192, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047016194, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047016229, "dur": 136, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047016369, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047016371, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047016412, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047016415, "dur": 115, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047016533, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047016535, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047016579, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047016581, "dur": 535, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047017121, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047017123, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047017170, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047017172, "dur": 79, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047017256, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047017258, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047017301, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047017304, "dur": 168, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047017477, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047017519, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047017522, "dur": 528, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047018054, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047018056, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047018095, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047018097, "dur": 48, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047018150, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047018185, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047018187, "dur": 182, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047018373, "dur": 95, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047018473, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047018474, "dur": 399, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047018877, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047018879, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047018929, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047018932, "dur": 469, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047019405, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047019407, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047019445, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047019447, "dur": 125373, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047144833, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047144839, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047144879, "dur": 810, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984047145697, "dur": 2934626, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984050080341, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984050080347, "dur": 158, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984050080509, "dur": 1969, "ph": "X", "name": "ProcessMessages 1702", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984050082482, "dur": 12794, "ph": "X", "name": "ReadAsync 1702", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984050095284, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984050095289, "dur": 90, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984050095381, "dur": 280, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 31244, "tid": 12884901888, "ts": 1751984050095664, "dur": 19350, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 31244, "tid": 18, "ts": 1751984051111137, "dur": 809, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 31244, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 31244, "tid": 8589934592, "ts": 1751984046037133, "dur": 1200814, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 31244, "tid": 8589934592, "ts": 1751984047237950, "dur": 7, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 31244, "tid": 8589934592, "ts": 1751984047237959, "dur": 2088, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 31244, "tid": 18, "ts": 1751984051111949, "dur": 6, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 31244, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 31244, "tid": 4294967296, "ts": 1751984045992059, "dur": 4124868, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 31244, "tid": 4294967296, "ts": 1751984045999160, "dur": 29236, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 31244, "tid": 4294967296, "ts": 1751984050117316, "dur": 572460, "ph": "X", "name": "await ExecuteBuildProgram", "args": {}}, {"pid": 31244, "tid": 4294967296, "ts": 1751984050689963, "dur": 400192, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 31244, "tid": 4294967296, "ts": 1751984050690099, "dur": 14721, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 31244, "tid": 4294967296, "ts": 1751984051090169, "dur": 7608, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 31244, "tid": 4294967296, "ts": 1751984051093630, "dur": 2752, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 31244, "tid": 4294967296, "ts": 1751984051097786, "dur": 13, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 31244, "tid": 18, "ts": 1751984051111956, "dur": 10, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751984050749273, "dur": 58, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751984050749362, "dur": 54445, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": ***********03813, "dur": 171, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": ***********04068, "dur": 355, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": ***********04567, "dur": 114, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_8519B72F30B7EEE3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********04751, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_B2E90B5842DD1C21.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********05552, "dur": 109, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_9E2287C3B1E222F4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********05728, "dur": 96, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_66F4F0C59F5004CE.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********05924, "dur": 108, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_8AC679A0A072FC67.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********06427, "dur": 94, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_1D62CA4F9CE86BBD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********06645, "dur": 95, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_7DE8E5E14EA73DFF.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********07017, "dur": 93, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_95038191186148C0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********07204, "dur": 108, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_0F47E031DE7CBED2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********07462, "dur": 93, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": ***********07849, "dur": 92, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": ***********08267, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 0, "ts": ***********09869, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": ***********10022, "dur": 100, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": ***********12146, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": ***********13029, "dur": 99, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": ***********13610, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********14435, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 0, "ts": ***********04463, "dur": 10108, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": ***********14583, "dur": 253463, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751984051068047, "dur": 133, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751984051070218, "dur": 110, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751984051070353, "dur": 1194, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": ***********05724, "dur": 8981, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********14716, "dur": 546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_986F749CC4F33028.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********15313, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_53D5AD7EC74B882D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********15470, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_D8A59059492883B0.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********15607, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_2A6A94B8015537C3.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********15737, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_9E2287C3B1E222F4.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********15834, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_3C49F2A4315F7028.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********15910, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_53CF5C60D786E1BC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********16011, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_3014A9005AF6E0C6.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********16199, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_54E7F0BDC1701621.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********16313, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_D37778ED904E6F34.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********16396, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_63A26A7278E8F511.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********16474, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_7AC9B62699C5AFAB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********16597, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********16702, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_37EA207A558A60E5.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********16825, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_B845EAF6ABDF1C86.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********16918, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_AEAC1BB5544068A9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********17022, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_A7D7A27FD520B061.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********17099, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_A761188D10ACF2F6.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********17183, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_8519B72F30B7EEE3.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********17311, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_B009007CB866ED03.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********17422, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_22DA848A55AE3E42.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********17678, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_22DA848A55AE3E42.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********17843, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********18064, "dur": 10614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": ***********28792, "dur": 401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********29200, "dur": 390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********29591, "dur": 821, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********30413, "dur": 532, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********30974, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********31193, "dur": 666, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": ***********31919, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********32111, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********32318, "dur": 724, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": ***********33124, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********33355, "dur": 630, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": ***********34064, "dur": 425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********34489, "dur": 280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********34769, "dur": 217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********34986, "dur": 447, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********35471, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********35634, "dur": 489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": ***********36288, "dur": 496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********36784, "dur": 231229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********05748, "dur": 9006, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********14756, "dur": 567, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_8E8F8EE39FADED60.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ***********15324, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********15381, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_09A4870D30E9488B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ***********15565, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_B98AA527428CA21A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ***********15643, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_ECF821B937ABA95C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ***********15721, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_F2E965C2FA451353.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ***********15804, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_7B65195D8C536D40.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ***********15878, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_128E18DD6FC8C69B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ***********15957, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_1DBEFCD96369F5BA.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ***********16038, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_447FE10D58FA1CDC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ***********16117, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_766A20F7659AD660.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ***********16196, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_F6B877512297B45B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ***********16274, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_5C0BC72BFAE5E8B3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ***********16357, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_1D072DB291F67E28.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ***********16431, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_B64929654506FD14.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ***********16503, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_D953FDA14B6C9CFB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ***********16573, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_4401640319B4850E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ***********16722, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_DF889B6FFC3E7D09.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ***********16806, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_F001EA08C606A7C7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ***********16886, "dur": 168, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_F001EA08C606A7C7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ***********17056, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_E3DEF0019A378312.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ***********17234, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_A2E87A4D8CFE3DDA.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ***********17328, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********17678, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 2, "ts": ***********17984, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": ***********19075, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********19393, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********19645, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********20244, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********20528, "dur": 504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********21032, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********21284, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********21594, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********21842, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********22098, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********22350, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********22674, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********22963, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********23295, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********23580, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********23870, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********24130, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********24389, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********24750, "dur": 1000, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\Decorators\\IDecoratorAttribute.cs"}}, {"pid": 12345, "tid": 2, "ts": ***********24641, "dur": 1323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********25964, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********26216, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********26467, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********26738, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********26974, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********27242, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********27490, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********27758, "dur": 1156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********28914, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********29207, "dur": 365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********29572, "dur": 867, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********30439, "dur": 529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********30970, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ***********31161, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********31307, "dur": 683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": ***********32134, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ***********32312, "dur": 734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": ***********33215, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********33291, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********33582, "dur": 169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********33751, "dur": 301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********34076, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********34507, "dur": 260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********34786, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********35004, "dur": 546, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********35551, "dur": 663, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********36214, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********36277, "dur": 537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********36814, "dur": 231230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********05544, "dur": 9121, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********14678, "dur": 575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_B2E90B5842DD1C21.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********15254, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********15543, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_8B6E2D36E99856BA.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********15636, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_5389693E2972069A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********15729, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_D130410B56E7FD0E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********15817, "dur": 170, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_D130410B56E7FD0E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********15995, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_ECE5B5A1A60989A9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********16097, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_F1579CE7AE5A4FEC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********16201, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_F2D9E52A8A1DA43D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********16285, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_FC37FAF5BBAF0F0E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********16359, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_B39AE0B6404CFC63.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********16450, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_FD883EED3964B210.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********16779, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_483FC56B1CE0C105.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********16868, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_8DDE3021FF9F8D2B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********16962, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_AE12D3B1B24D183C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********17074, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_4191D5C1BDD09DE5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********17144, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_DA1FDB9199E74054.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********17222, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_CA3B36F6C19A89E9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********17307, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_4A503D7C9B535AEF.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********17375, "dur": 397, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_4A503D7C9B535AEF.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********17785, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********17946, "dur": 11554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": ***********29600, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********29713, "dur": 638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": ***********30427, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********30540, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": ***********30964, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********31170, "dur": 1559, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": ***********32777, "dur": 387, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********33173, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********33393, "dur": 1039, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": ***********34506, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********34613, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": ***********34991, "dur": 560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********35551, "dur": 743, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********36294, "dur": 406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********36821, "dur": 231180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********05347, "dur": 9252, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********18512, "dur": 272, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.10f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 4, "ts": ***********14609, "dur": 4176, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********18936, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": ***********20279, "dur": 506, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 4, "ts": ***********20789, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********21051, "dur": 358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********21409, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********21668, "dur": 390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********22059, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********22308, "dur": 390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********22699, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********23219, "dur": 627, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Plugin\\Acknowledgements\\Acknowledgement_ReorderableList.cs"}}, {"pid": 12345, "tid": 4, "ts": ***********22993, "dur": 883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********23876, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********24111, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********24356, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********24763, "dur": 989, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Reflection\\fsTypeCache.cs"}}, {"pid": 12345, "tid": 4, "ts": ***********24611, "dur": 1327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********25939, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********26198, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********26450, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********26715, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********26948, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********27196, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********27422, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********27661, "dur": 1113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********28798, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********29029, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********29260, "dur": 310, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********29613, "dur": 792, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********30457, "dur": 477, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********30967, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": ***********31218, "dur": 584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": ***********31803, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********32136, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": ***********32321, "dur": 757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": ***********33079, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********33174, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": ***********33446, "dur": 1275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": ***********34784, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": ***********34898, "dur": 485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": ***********35465, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": ***********35633, "dur": 597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": ***********36283, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": ***********36396, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": ***********36793, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": ***********37271, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 4, "ts": ***********36946, "dur": 438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": ***********37506, "dur": 220410, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": ***********05371, "dur": 9249, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********14637, "dur": 581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_11DB9A34FA868FF7.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": ***********15263, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_FDB4EFCD5ACD4A7E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": ***********15523, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_676ABA2C7AEF0941.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": ***********15868, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_66F4F0C59F5004CE.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": ***********15955, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_8370AE51D55B6124.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": ***********16101, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_842839871A1EA450.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": ***********16246, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_9C617BE0309AD0B2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": ***********16325, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_6E3EBF5853783B2F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": ***********16406, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_BF7F8C87F103B32C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": ***********16506, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_6D3089BB8D2919C6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": ***********16612, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_7DE8E5E14EA73DFF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": ***********16804, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_548EF2076EF26D7E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": ***********16904, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_8B820BD2AC6A239F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": ***********17019, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_02AC65D6454A1D51.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": ***********17111, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_0F47E031DE7CBED2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": ***********17185, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_75D831B6A540D53E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": ***********17439, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_1280E01E43015E29.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": ***********17511, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_6470942D9C999E52.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": ***********17618, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********17685, "dur": 507, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_6470942D9C999E52.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": ***********18208, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 5, "ts": ***********18526, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": ***********18797, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": ***********19060, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********19351, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********19592, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********19837, "dur": 617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********20454, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********20700, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********20953, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********21274, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********21593, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********21859, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********22110, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********22374, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********22684, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********22940, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********23248, "dur": 589, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Meta\\MemberMetadata.cs"}}, {"pid": 12345, "tid": 5, "ts": ***********23202, "dur": 824, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********24026, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********24341, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********24761, "dur": 994, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\Unity\\Bounds_DirectConverter.cs"}}, {"pid": 12345, "tid": 5, "ts": ***********24628, "dur": 1309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********25937, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********26176, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********26437, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********26678, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********26903, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********27133, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********27381, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********27615, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********27847, "dur": 1145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********28993, "dur": 354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********29347, "dur": 222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********29617, "dur": 786, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********30452, "dur": 484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********30971, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": ***********31281, "dur": 588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": ***********31875, "dur": 389, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********32332, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": ***********32513, "dur": 670, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": ***********33184, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********33409, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********33592, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********33750, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********34053, "dur": 441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********34495, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********34770, "dur": 204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********34997, "dur": 480, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********35503, "dur": 789, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********36292, "dur": 459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********36752, "dur": 231295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********05411, "dur": 9226, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********14646, "dur": 536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_E605164F31FC9E53.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": ***********15209, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_CDB1A95E7E145EAF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": ***********15316, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_1CB45D079AA45272.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": ***********15398, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_D71F57A4BA15FD36.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": ***********15483, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_D71F57A4BA15FD36.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": ***********15539, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_424F017654AB0835.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": ***********15613, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_D38426C52D066A7D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": ***********15691, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_479158231A9CA7BA.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": ***********15766, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_D7BD4DAE2A87750D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": ***********15845, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_C5DA40F663BC1690.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": ***********15923, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_1C885156FB3E8240.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": ***********15996, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_98F905094D464069.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": ***********16068, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_35660A69CD57B1E7.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": ***********16143, "dur": 406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_B3648F7EA00CC91E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": ***********16569, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_F834F25F3A2ABA1A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": ***********16732, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_63005792C3CA19A5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": ***********16905, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_9A1191F70F63F6BB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": ***********17005, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3CCE213C8D987695.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": ***********17315, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********17415, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_D6C87365A8B0A170.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": ***********17540, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_5EE0364917308BF5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": ***********17656, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": ***********17787, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 6, "ts": ***********17945, "dur": 255, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 6, "ts": ***********18786, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": ***********19063, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********19360, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********19610, "dur": 636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********20247, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********20513, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********20813, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********21050, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********21371, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********21614, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********21839, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********22076, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********22334, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********22655, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********22898, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********23163, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********23423, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********23658, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********23967, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********24209, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********24446, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********24751, "dur": 994, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\Collections\\ISet.cs"}}, {"pid": 12345, "tid": 6, "ts": ***********24674, "dur": 1329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********26003, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********26308, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********26553, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********26779, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********27037, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********27291, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********27534, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********27774, "dur": 1308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********29204, "dur": 369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********29573, "dur": 859, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********30433, "dur": 504, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********30975, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": ***********31215, "dur": 1268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": ***********32487, "dur": 442, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********32936, "dur": 724, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": ***********33744, "dur": 297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********34079, "dur": 407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********34517, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********34782, "dur": 193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********34975, "dur": 523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********35498, "dur": 795, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********36294, "dur": 453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********36803, "dur": 231204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********05696, "dur": 8990, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********14698, "dur": 455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_7B346C974D1F5AEB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ***********15207, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_53494DA4BD956B35.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ***********15327, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_0FE6296F75202CCA.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ***********15450, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_488457505398C519.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ***********15517, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********15715, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_71A26EB5B308E8FC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ***********15843, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_591B53DA12AB67D2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ***********15998, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_8AC679A0A072FC67.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ***********16082, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_F0278B1129FD0781.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ***********16154, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_E83DAECB7A832050.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ***********16225, "dur": 337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_82FE19077176A6E4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ***********16562, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********16691, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_6EB9682B6CBB35BC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ***********17000, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_08577EFB2D8C4EDA.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ***********17092, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_5D919F001659B1C7.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ***********17173, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_84D6A8621C493071.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ***********17373, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 7, "ts": ***********17451, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********17508, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_28DAF9479429A673.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ***********17590, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 7, "ts": ***********17700, "dur": 279, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 7, "ts": ***********17979, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 7, "ts": ***********18138, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********18283, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********18333, "dur": 392, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 7, "ts": ***********18767, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********18913, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": ***********19057, "dur": 543, "ph": "X", "name": "File", "args": {"detail": "Assets\\VisualizationController.cs"}}, {"pid": 12345, "tid": 7, "ts": ***********19057, "dur": 1265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********20322, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********20569, "dur": 360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********20929, "dur": 357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********21287, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********21547, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********21785, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********22066, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********22317, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********22645, "dur": 615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********23260, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********23516, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********23807, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********24055, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********24337, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********24751, "dur": 968, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\fsISerializationCallbacks.cs"}}, {"pid": 12345, "tid": 7, "ts": ***********24624, "dur": 1294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********25919, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********26235, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********26480, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********26706, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********26929, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********27161, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********27380, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********27617, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********27851, "dur": 1185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********29194, "dur": 377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********29571, "dur": 869, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********30440, "dur": 495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********30965, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ***********31173, "dur": 558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": ***********31732, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********31917, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ***********32088, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ***********32160, "dur": 736, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": ***********32940, "dur": 1067, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": ***********34070, "dur": 432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********34502, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********34791, "dur": 184, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********34975, "dur": 512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********35487, "dur": 725, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********36290, "dur": 520, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********36810, "dur": 231194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********05464, "dur": 9186, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********14660, "dur": 578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_06CF1E61B9937147.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": ***********15265, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_7127B1C813DA0DCC.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": ***********15367, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_34FDE67FD72C093F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": ***********15452, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_1D12CBD0225DA556.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": ***********15606, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_5347A9488345F9E4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": ***********15809, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_9BB14A022E6A0C89.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": ***********15946, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********16002, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_1B2C760BE566AFA1.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": ***********16204, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_E514777F6F7897AD.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": ***********16404, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_1D62CA4F9CE86BBD.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": ***********16556, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_1B89DE15EED7BC0C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": ***********16728, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_1CE54D6266641F08.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": ***********16811, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_975CFD6F88652DB3.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": ***********16964, "dur": 331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_95038191186148C0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": ***********17296, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********17658, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": ***********17782, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********17844, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********17902, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": ***********18074, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********18166, "dur": 933, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": ***********19136, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********19453, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********19711, "dur": 656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********20367, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********20596, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********20848, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********21102, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********21384, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********21653, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********21915, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********22149, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********22478, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********22712, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********22968, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********23268, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********23531, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********23748, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********24021, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********24299, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********24753, "dur": 992, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\EditorBinding\\Inspector\\InspectorToggleLeftAttribute.cs"}}, {"pid": 12345, "tid": 8, "ts": ***********24565, "dur": 1297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********25863, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********26152, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********26389, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********26615, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********26841, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********27095, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********27355, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********27633, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********27880, "dur": 1257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********29199, "dur": 382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********29581, "dur": 833, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********30414, "dur": 524, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********30973, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": ***********31183, "dur": 651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": ***********31901, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": ***********32123, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": ***********32328, "dur": 606, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": ***********32935, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********33579, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********33745, "dur": 315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********34060, "dur": 428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********34488, "dur": 291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********34780, "dur": 202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********34982, "dur": 498, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********35480, "dur": 741, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********36270, "dur": 512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********36782, "dur": 231263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751984051074292, "dur": 15331, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "netcorerun.dll"}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-1"}}, {"pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 35942, "tid": 1, "ts": 1751984050284985, "dur": 374559, "ph": "X", "name": "BuildProgram", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1751984050286837, "dur": 122266, "ph": "X", "name": "BuildProgramContextConstructor", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1751984050589422, "dur": 4496, "ph": "X", "name": "OutputData.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1751984050593922, "dur": 65609, "ph": "X", "name": "Backend.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1751984050596451, "dur": 40894, "ph": "X", "name": "JsonToString", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1751984050669703, "dur": 1176, "ph": "X", "name": "", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1751984050669182, "dur": 1912, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751984046603326, "dur": 60, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751984046603426, "dur": 241839, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751984046845283, "dur": 390, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751984046845769, "dur": 369, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751984046846338, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_11DB9A34FA868FF7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751984046847295, "dur": 359, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_F2E965C2FA451353.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751984046847662, "dur": 200, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_D130410B56E7FD0E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751984046848648, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_6E3EBF5853783B2F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751984046849726, "dur": 213, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751984046850014, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_5EE0364917308BF5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751984046850185, "dur": 121, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751984046850569, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751984046851120, "dur": 111, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Settings.Editor.ref.dll_7F5B154C05A780B0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751984046851313, "dur": 115, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751984046852155, "dur": 117, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.Editor.ref.dll_EDC8690F57C5BDFD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751984046852582, "dur": 717, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751984046854911, "dur": 199, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751984046855338, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751984046856196, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Shared.Editor.ref.dll_9669F6EBFF6DFFB7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751984046856573, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751984046856743, "dur": 393, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751984046857192, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751984046857667, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751984046857852, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751984046858816, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751984046858926, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751984046859028, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751984046859271, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751984046846178, "dur": 13370, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751984046859562, "dur": 392629, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751984047252193, "dur": 244, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751984047252488, "dur": 2845913, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751984050098539, "dur": 163, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751984050098760, "dur": 1532, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751984046847093, "dur": 12617, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984046859712, "dur": 111327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_8E8F8EE39FADED60.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751984046971088, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_8E8F8EE39FADED60.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751984046971214, "dur": 7518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_D71F57A4BA15FD36.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751984046978734, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984046978917, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_9BB14A022E6A0C89.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751984046979202, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_53CF5C60D786E1BC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751984046979400, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_98F905094D464069.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751984046979521, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_35660A69CD57B1E7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751984046979635, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_766A20F7659AD660.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751984046979764, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_F6B877512297B45B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751984046979918, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_D37778ED904E6F34.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751984046980231, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_6D3089BB8D2919C6.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751984046980377, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_37EA207A558A60E5.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751984046980732, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_483FC56B1CE0C105.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751984046980841, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_8B820BD2AC6A239F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751984046980959, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_AE12D3B1B24D183C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751984046981091, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3CCE213C8D987695.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751984046981190, "dur": 937, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984046982179, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751984046982278, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984046982376, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751984046982479, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984046982529, "dur": 298, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751984046982898, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984046983575, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984046983867, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751984046983948, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751984046984027, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751984046984106, "dur": 613, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984046984731, "dur": 698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984046985430, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984046986019, "dur": 1012, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984046987319, "dur": 1091, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage@205a02cbcb39\\Editor\\CoverageWindow\\PathToAddHandler.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751984046987032, "dur": 1599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984046988631, "dur": 1050, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984046989682, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984046990268, "dur": 1147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984046991416, "dur": 2272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984046993689, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984046994086, "dur": 675, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Widgets\\Nodes\\NodeShape.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751984046994779, "dur": 1087, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Widgets\\Nodes\\NodeColor.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751984046996235, "dur": 522, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Variables\\VariablesEditor.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751984046993988, "dur": 2783, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984046997289, "dur": 550, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Interface\\Dropdowns\\DropdownOption.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751984046996771, "dur": 1234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984046998005, "dur": 933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984046998938, "dur": 501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984046999440, "dur": 616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984047000057, "dur": 1545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984047001603, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984047001878, "dur": 729, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\Cloning\\Cloners\\FakeSerializationCloner.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751984047001865, "dur": 1588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984047003457, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984047003842, "dur": 854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984047004697, "dur": 815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984047005512, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984047006192, "dur": 1181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984047007374, "dur": 3625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984047011000, "dur": 870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984047011871, "dur": 1114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984047012986, "dur": 873, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984047013859, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984047014008, "dur": 817, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984047014825, "dur": 894, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984047015720, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751984047015923, "dur": 551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751984047016475, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984047016579, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751984047016737, "dur": 1301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751984047018038, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984047018262, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751984047018435, "dur": 521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751984047019030, "dur": 690, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984047019721, "dur": 183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984047019904, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984047020159, "dur": 457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984047020651, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984047020804, "dur": 780, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984047021677, "dur": 645, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751984047022359, "dur": 229758, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984046847062, "dur": 12654, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984046859717, "dur": 111394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_53494DA4BD956B35.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751984046971164, "dur": 7496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_34FDE67FD72C093F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751984046978663, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984046978778, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_D7BD4DAE2A87750D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751984046979053, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_C5DA40F663BC1690.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751984046979275, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_1DBEFCD96369F5BA.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751984046979432, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_1B2C760BE566AFA1.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751984046979535, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_F0278B1129FD0781.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751984046979640, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_B3648F7EA00CC91E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751984046979886, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_9C617BE0309AD0B2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751984046979992, "dur": 690, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_1D072DB291F67E28.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751984046980741, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_F001EA08C606A7C7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751984046980955, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_AEAC1BB5544068A9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751984046981095, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_02AC65D6454A1D51.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751984046981247, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_5D919F001659B1C7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751984046981367, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_DA1FDB9199E74054.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751984046981495, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_75D831B6A540D53E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751984046981616, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_4A503D7C9B535AEF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751984046981869, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_7F371AF1A9E7F3FC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751984046982203, "dur": 1432, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984046983793, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751984046983977, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751984046984063, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751984046984172, "dur": 716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984046984889, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984046985537, "dur": 504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984046986041, "dur": 774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984046986816, "dur": 699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984046987516, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984046988180, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984046988499, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984046989177, "dur": 1297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984046990475, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984046991022, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984046991458, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984046991727, "dur": 1393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984046993121, "dur": 925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984046994087, "dur": 1151, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Variables\\VariableDeclarationInspector.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751984046994046, "dur": 1856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984046995902, "dur": 636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984046996593, "dur": 581, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Interface\\Fuzzy\\FuzzyOptionTreeExtensionAttribute.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751984046996539, "dur": 1570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984046998109, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984046998775, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984046999324, "dur": 743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984047000699, "dur": 511, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\Listeners\\MonoBehaviour\\UnityOnTransformParentChangedMListener.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751984047000067, "dur": 1544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984047001848, "dur": 759, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\fsDateConverter.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751984047001611, "dur": 996, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984047002608, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984047002880, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984047003101, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984047003320, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984047003617, "dur": 968, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984047004585, "dur": 1663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984047006744, "dur": 685, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@7056e7f856e9\\UnityEditor.TestRunner\\TestRun\\Tasks\\BuildActionTaskBase.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751984047006250, "dur": 1246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984047007496, "dur": 3377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984047010873, "dur": 892, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984047011765, "dur": 1899, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984047013665, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984047013840, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984047014016, "dur": 883, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984047014900, "dur": 894, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984047015796, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751984047015994, "dur": 618, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751984047016685, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751984047016850, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751984047017077, "dur": 534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751984047017612, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984047017937, "dur": 971, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751984047019021, "dur": 614, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984047019659, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984047019907, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984047020077, "dur": 575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984047020652, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984047020786, "dur": 817, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984047021603, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984047021669, "dur": 664, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751984047022333, "dur": 229809, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984046847133, "dur": 12554, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984046859688, "dur": 110016, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_986F749CC4F33028.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751984046969726, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984046969826, "dur": 1225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_7127B1C813DA0DCC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751984046971107, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_53D5AD7EC74B882D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751984046971345, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_488457505398C519.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751984046971437, "dur": 475, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984046971923, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_5347A9488345F9E4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751984046972056, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_5389693E2972069A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751984046972192, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_ECF821B937ABA95C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751984046972309, "dur": 7422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_479158231A9CA7BA.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751984046979760, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_E83DAECB7A832050.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751984046979881, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_82FE19077176A6E4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751984046979989, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_6E3EBF5853783B2F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751984046980104, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_BF7F8C87F103B32C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751984046980243, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_1B89DE15EED7BC0C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751984046980353, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_6EB9682B6CBB35BC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751984046980437, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984046980619, "dur": 542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_63005792C3CA19A5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751984046981194, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_4191D5C1BDD09DE5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751984046981333, "dur": 331, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_4191D5C1BDD09DE5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751984046981731, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751984046981813, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_D6C87365A8B0A170.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751984046982011, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_D01CC5A6D0F5DAF9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751984046982512, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751984046982663, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984046982753, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984046982862, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751984046982944, "dur": 1774, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984046984729, "dur": 1440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984046986170, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984046987826, "dur": 553, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@57cef44123c7\\Editor\\TMP\\TMP_EditorPanelUI.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751984046986821, "dur": 1604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984046988426, "dur": 1004, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984046989431, "dur": 699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984046990130, "dur": 628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984046990758, "dur": 1125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984046991884, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984046992430, "dur": 1082, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984046993513, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984046994102, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984046994394, "dur": 1038, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Serialization\\TypeExtensions.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751984046995476, "dur": 585, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\SemanticLabel.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751984046994286, "dur": 1835, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984046996167, "dur": 798, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Plugin\\Changelogs\\Changelog_1_3_0.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751984046996121, "dur": 1562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984046997684, "dur": 1013, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984046999486, "dur": 525, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\SerializedProperties\\ISerializedPropertyProvider.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751984047000776, "dur": 503, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\Serialization\\SerializableType.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751984046998697, "dur": 2611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984047001308, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984047002007, "dur": 597, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\Collections\\DebugDictionary.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751984047001855, "dur": 1434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984047003289, "dur": 355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984047003644, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984047004300, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984047004959, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984047005252, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984047005496, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984047006057, "dur": 980, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984047007038, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984047007266, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984047008322, "dur": 1107, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.10f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751984047007715, "dur": 4160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984047011996, "dur": 1858, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984047013855, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984047014012, "dur": 797, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984047014844, "dur": 850, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984047015724, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751984047015930, "dur": 564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751984047016561, "dur": 396, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984047016962, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751984047017181, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984047017251, "dur": 643, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751984047017950, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984047018023, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751984047018197, "dur": 1378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751984047019653, "dur": 258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984047019912, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984047020070, "dur": 584, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984047020654, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984047020794, "dur": 801, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984047021596, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984047021676, "dur": 649, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751984047022326, "dur": 229827, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751984046847242, "dur": 12415, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751984046859659, "dur": 110065, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_7B346C974D1F5AEB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751984046969733, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751984046969823, "dur": 1227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_FDB4EFCD5ACD4A7E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751984046971112, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_1CB45D079AA45272.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751984046971348, "dur": 7329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_1D12CBD0225DA556.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751984046978678, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751984046978759, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_D130410B56E7FD0E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751984046978972, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_3C49F2A4315F7028.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751984046979105, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_66F4F0C59F5004CE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751984046979225, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_1C885156FB3E8240.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751984046979348, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_8AC679A0A072FC67.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751984046979469, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_3014A9005AF6E0C6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751984046979780, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_F2D9E52A8A1DA43D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751984046979903, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_5C0BC72BFAE5E8B3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751984046980033, "dur": 729, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_63A26A7278E8F511.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751984046980790, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_975CFD6F88652DB3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751984046980924, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_9A1191F70F63F6BB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751984046981027, "dur": 682, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_08577EFB2D8C4EDA.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751984046981818, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_22DA848A55AE3E42.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751984046981933, "dur": 1015, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751984046982979, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751984046983331, "dur": 30404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751984047013854, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751984047014012, "dur": 806, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751984047014819, "dur": 901, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751984047015721, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751984047015921, "dur": 567, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751984047016489, "dur": 576, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751984047017100, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751984047017331, "dur": 704, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751984047018036, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751984047018630, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751984047018725, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751984047018902, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751984047019058, "dur": 575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751984047019662, "dur": 223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751984047019912, "dur": 237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751984047020149, "dur": 469, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751984047020647, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751984047020801, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751984047020974, "dur": 521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751984047021495, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751984047021591, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751984047021680, "dur": 644, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751984047022325, "dur": 220928, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751984047243257, "dur": 8837, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984046847002, "dur": 12593, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984046859604, "dur": 111849, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_E605164F31FC9E53.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751984046971479, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_676ABA2C7AEF0941.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751984046971592, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_424F017654AB0835.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751984046971700, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_8B6E2D36E99856BA.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751984046971852, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_B98AA527428CA21A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751984046972053, "dur": 409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_D38426C52D066A7D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751984046972506, "dur": 6133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_71A26EB5B308E8FC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751984046978641, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984046978853, "dur": 719, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_7B65195D8C536D40.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751984046979596, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_F1579CE7AE5A4FEC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751984046979677, "dur": 478, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984046980161, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_FD883EED3964B210.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751984046980307, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_F834F25F3A2ABA1A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751984046980415, "dur": 691, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_DF889B6FFC3E7D09.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751984046981159, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_E3DEF0019A378312.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751984046981423, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984046981557, "dur": 417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_CA3B36F6C19A89E9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751984046982031, "dur": 751, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_6470942D9C999E52.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751984046982783, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984046982903, "dur": 1017, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984046983941, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751984046984015, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984046984189, "dur": 329, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984046984529, "dur": 1870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984046986401, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984046987079, "dur": 748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984046987828, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984046988594, "dur": 1257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984046989851, "dur": 1002, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984046990854, "dur": 1024, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984046991879, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984046992178, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984046992838, "dur": 897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984046993841, "dur": 800, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Connections\\InvalidConnection.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751984046994677, "dur": 932, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Windows\\WindowClose.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751984046995609, "dur": 541, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Windows\\WebWindow.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751984046993736, "dur": 2503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984046996239, "dur": 1048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984046997288, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984046997498, "dur": 891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984046998390, "dur": 1624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984047000014, "dur": 1107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984047001133, "dur": 553, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\Events\\EventHooks.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751984047001122, "dur": 785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984047002027, "dur": 562, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Runtime\\Scripting\\PlayableTrack.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751984047001907, "dur": 877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984047002785, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984047003020, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984047003324, "dur": 577, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage@205a02cbcb39\\Editor\\CoverageFormats\\OpenCover\\Model\\SummarySkippedEntity.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751984047003243, "dur": 821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984047004065, "dur": 523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984047004589, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984047004795, "dur": 887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984047005683, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984047005930, "dur": 539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984047006470, "dur": 821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984047007292, "dur": 1670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984047008963, "dur": 1087, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984047010051, "dur": 940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984047010992, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984047011562, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984047011749, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984047012112, "dur": 1750, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984047013863, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984047014005, "dur": 822, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984047014827, "dur": 883, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984047015711, "dur": 920, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984047016632, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751984047016925, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751984047017133, "dur": 645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751984047017833, "dur": 752, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751984047018874, "dur": 140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984047019014, "dur": 648, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984047019662, "dur": 231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984047019924, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751984047020076, "dur": 639, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751984047020799, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751984047020972, "dur": 630, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751984047021672, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751984047021862, "dur": 398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751984047022343, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751984047022476, "dur": 410, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984047022894, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751984047147675, "dur": 183, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751984047239218, "dur": 2845026, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751984046846852, "dur": 12725, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751984046859618, "dur": 111460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_11DB9A34FA868FF7.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751984046971080, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751984046971153, "dur": 7519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_0FE6296F75202CCA.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751984046978674, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751984046978752, "dur": 995, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_F2E965C2FA451353.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751984046979776, "dur": 977, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_54E7F0BDC1701621.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751984046980755, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751984046980830, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_B845EAF6ABDF1C86.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751984046981120, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_A7D7A27FD520B061.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751984046981268, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_A7D7A27FD520B061.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751984046981364, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_0F47E031DE7CBED2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751984046981620, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_B009007CB866ED03.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751984046981844, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_8189EC8801BEEB18.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751984046982082, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_5EE0364917308BF5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751984046982336, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751984046982490, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751984046982803, "dur": 426, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751984046983231, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751984046983445, "dur": 566, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751984046984016, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751984046984137, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751984046984210, "dur": 483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751984046984694, "dur": 1381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751984046986156, "dur": 607, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\inspectors\\MarkerInspector.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751984046986076, "dur": 901, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751984046987442, "dur": 862, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage@205a02cbcb39\\Editor\\ReportGenerator\\ReportGeneratorStyles.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751984046986978, "dur": 1437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751984046988416, "dur": 616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751984046989123, "dur": 606, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.State\\NesterStateTransition.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751984046989033, "dur": 1589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751984046990623, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751984046990919, "dur": 1636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751984046992962, "dur": 504, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Framework\\Events\\GUI\\OnGUI.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751984046993909, "dur": 734, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Framework\\Events\\GUI\\GenericGuiEventUnit.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751984046992555, "dur": 2134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751984046994689, "dur": 543, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\ResourceProviders\\CreateTextureOptions.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751984046995336, "dur": 538, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Reflection\\LooseAssemblyNameOptionTree.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751984046994689, "dur": 1309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751984046995999, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751984046996371, "dur": 549, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Meta\\DictionaryIndexMetadata.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751984046997217, "dur": 535, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Interface\\SharedEditorTextureDictionary.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751984046996305, "dur": 1578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751984046997884, "dur": 628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751984046998513, "dur": 1591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751984047000105, "dur": 633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751984047000739, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751984047001421, "dur": 1172, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\EditorBinding\\Inspector\\InspectViaImplementationsAttribute.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751984047002758, "dur": 657, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\EditorBinding\\Inspector\\InspectorRangeAttribute.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751984047001289, "dur": 2212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751984047003501, "dur": 968, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751984047004470, "dur": 913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751984047005383, "dur": 1246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751984047006660, "dur": 727, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@7056e7f856e9\\UnityEditor.TestRunner\\TestLaunchers\\PlatformSetup\\AndroidPlatformSetup.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751984047006630, "dur": 1346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751984047007977, "dur": 5336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751984047013313, "dur": 514, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751984047013866, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751984047014030, "dur": 803, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751984047014833, "dur": 1004, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751984047015838, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751984047016086, "dur": 718, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751984047016804, "dur": 1370, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751984047018174, "dur": 321, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751984047018527, "dur": 371, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_621CDDF9C514DF8F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751984047018900, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751984047019100, "dur": 621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751984047019721, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751984047020083, "dur": 729, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751984047020812, "dur": 798, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751984047021686, "dur": 634, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751984047022363, "dur": 229756, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751984046847184, "dur": 12431, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751984046859616, "dur": 111499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_B2E90B5842DD1C21.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751984046971117, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751984046971190, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_09A4870D30E9488B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751984046971392, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_D8A59059492883B0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751984046971559, "dur": 431, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751984046971999, "dur": 8054, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_2A6A94B8015537C3.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751984046980080, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_1D62CA4F9CE86BBD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751984046980201, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_7AC9B62699C5AFAB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751984046980314, "dur": 375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_4401640319B4850E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751984046980738, "dur": 595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_548EF2076EF26D7E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751984046981361, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_A761188D10ACF2F6.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751984046981489, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_8519B72F30B7EEE3.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751984046981613, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_A2E87A4D8CFE3DDA.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751984046981700, "dur": 1165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751984046982883, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751984046983120, "dur": 30823, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751984047014028, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751984047014151, "dur": 605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751984047014806, "dur": 361, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751984047015180, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751984047015296, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751984047015719, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751984047015988, "dur": 1877, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751984047017865, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751984047018013, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751984047018259, "dur": 1528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751984047019917, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751984047020078, "dur": 473, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751984047020637, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751984047020802, "dur": 768, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751984047021608, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751984047021663, "dur": 676, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751984047022339, "dur": 229792, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984046847278, "dur": 12330, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984046859609, "dur": 110126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_06CF1E61B9937147.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751984046969818, "dur": 8808, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_CDB1A95E7E145EAF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751984046978629, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984046978775, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_9E2287C3B1E222F4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751984046978986, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_591B53DA12AB67D2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751984046979118, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_128E18DD6FC8C69B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751984046979230, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_8370AE51D55B6124.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751984046979355, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_ECE5B5A1A60989A9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751984046979517, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_447FE10D58FA1CDC.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751984046979631, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_842839871A1EA450.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751984046979791, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_E514777F6F7897AD.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751984046979915, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_FC37FAF5BBAF0F0E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751984046980020, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_B39AE0B6404CFC63.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751984046980123, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_B64929654506FD14.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751984046980226, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_D953FDA14B6C9CFB.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751984046980330, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_7DE8E5E14EA73DFF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751984046980427, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984046980574, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_1CE54D6266641F08.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751984046980835, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_8DDE3021FF9F8D2B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751984046981001, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_95038191186148C0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751984046981083, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984046981331, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_95038191186148C0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751984046981470, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_84D6A8621C493071.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751984046981761, "dur": 164, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751984046981927, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_1280E01E43015E29.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751984046982165, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751984046982498, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751984046982661, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751984046982798, "dur": 239, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751984046983632, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751984046983908, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751984046983985, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751984046984226, "dur": 959, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984046986263, "dur": 621, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\treeview\\Drawers\\AnimationTrackDrawer.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751984046985186, "dur": 1699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984046986885, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984046988217, "dur": 505, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider@4d374c7eb6db\\Rider\\Editor\\RiderScriptEditorDataPersisted.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751984046987199, "dur": 1553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984046989192, "dur": 635, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.State\\Analytics\\StateMacroSavedEvent.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751984046988752, "dur": 1256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984046990015, "dur": 1363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984046991379, "dur": 656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984046992035, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984046992583, "dur": 1202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984046994462, "dur": 630, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Windows\\AboutWindow\\IAboutable.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751984046993786, "dur": 1367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984046995237, "dur": 671, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Plugins\\PluginModuleDependencyAttribute.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751984046995154, "dur": 907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984046996758, "dur": 631, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Plugin\\Changelogs\\LegacyLudiqCore\\Changelog_1_0_1.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751984046996061, "dur": 1371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984046997433, "dur": 819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984046998252, "dur": 523, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Assignment\\AssignsAttribute.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751984046998252, "dur": 1439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984046999692, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984047000171, "dur": 1480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984047002020, "dur": 546, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\Connections\\GraphConnectionCollection.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751984047001652, "dur": 1627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984047003280, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984047003936, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984047004202, "dur": 618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984047004820, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984047005095, "dur": 514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984047005609, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984047006164, "dur": 880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984047007044, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984047007269, "dur": 718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984047007988, "dur": 5367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984047013356, "dur": 466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984047013861, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984047014029, "dur": 774, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984047014827, "dur": 871, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984047015726, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751984047015964, "dur": 669, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751984047016634, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984047016814, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751984047017034, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984047017205, "dur": 807, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751984047018013, "dur": 557, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984047018646, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984047018768, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984047018876, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984047019005, "dur": 661, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984047019666, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984047019910, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984047020153, "dur": 488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984047020641, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984047020811, "dur": 862, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984047021673, "dur": 660, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751984047022333, "dur": 229845, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751984050116991, "dur": 1144, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 31244, "tid": 18, "ts": 1751984051112496, "dur": 2900, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend2.traceevents"}}, {"pid": 31244, "tid": 18, "ts": 1751984051117603, "dur": 65, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram0.traceevents"}}, {"pid": 31244, "tid": 18, "ts": 1751984051117824, "dur": 71, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 31244, "tid": 18, "ts": 1751984051115451, "dur": 2145, "ph": "X", "name": "backend2.traceevents", "args": {}}, {"pid": 31244, "tid": 18, "ts": 1751984051117708, "dur": 115, "ph": "X", "name": "buildprogram0.traceevents", "args": {}}, {"pid": 31244, "tid": 18, "ts": 1751984051117927, "dur": 745, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 31244, "tid": 18, "ts": 1751984051106000, "dur": 14370, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}