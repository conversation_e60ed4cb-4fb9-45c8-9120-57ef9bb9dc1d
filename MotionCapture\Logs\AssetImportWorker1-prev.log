Using pre-set license
Built from '6000.1/staging' branch; Version is '6000.1.10f1 (3c681a6c22ff) revision 3958810'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 7926 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1
Date: 2025-07-08T12:53:13Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.1.10f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
E:/Sem 5-9/7th sem/F1 - Game Programming/Project/3D Motion Capture using Normal Webcam/MotionCapture
-logFile
Logs/AssetImportWorker1.log
-srvPort
50795
-job-worker-count
3
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: E:/Sem 5-9/7th sem/F1 - Game Programming/Project/3D Motion Capture using Normal Webcam/MotionCapture
E:/Sem 5-9/7th sem/F1 - Game Programming/Project/3D Motion Capture using Normal Webcam/MotionCapture
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [16312]  Target information:

Player connection [16312]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 59312134 [EditorId] 59312134 [Version] 1048832 [Id] WindowsEditor(7,Sanjanesh) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [16312] Host joined multi-casting on [***********:54997]...
Player connection [16312] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 3
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 4.78 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.1.10f1 (3c681a6c22ff)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.1.10f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/Sem 5-9/7th sem/F1 - Game Programming/Project/3D Motion Capture using Normal Webcam/MotionCapture/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce MX350 (ID=0x1c94)
    Vendor:   NVIDIA
    VRAM:     1964 MB
    Driver:   32.0.15.7283
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.1.10f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.1.10f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.1.10f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56124
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.10f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.067328 seconds.
- Loaded All Assemblies, in  1.831 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.095 seconds
Domain Reload Profiling: 2914ms
	BeginReloadAssembly (309ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (2ms)
	RebuildCommonClasses (97ms)
	RebuildNativeTypeToScriptingClass (30ms)
	initialDomainReloadingComplete (527ms)
	LoadAllAssembliesAndSetupDomain (855ms)
		LoadAssemblies (297ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (769ms)
			TypeCache.Refresh (764ms)
				TypeCache.ScanAssembly (725ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (2ms)
	FinalizeReload (1095ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (995ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (148ms)
			SetLoadedEditorAssemblies (12ms)
			BeforeProcessingInitializeOnLoad (413ms)
			ProcessInitializeOnLoadAttributes (285ms)
			ProcessInitializeOnLoadMethodAttributes (135ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  3.899 seconds
Refreshing native plugins compatible for Editor in 205.26 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.799 seconds
Domain Reload Profiling: 6689ms
	BeginReloadAssembly (560ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (15ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (109ms)
	RebuildCommonClasses (87ms)
	RebuildNativeTypeToScriptingClass (33ms)
	initialDomainReloadingComplete (83ms)
	LoadAllAssembliesAndSetupDomain (3126ms)
		LoadAssemblies (2393ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1062ms)
			TypeCache.Refresh (806ms)
				TypeCache.ScanAssembly (735ms)
			BuildScriptInfoCaches (220ms)
			ResolveRequiredComponents (26ms)
	FinalizeReload (2800ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2341ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (368ms)
			ProcessInitializeOnLoadAttributes (1871ms)
			ProcessInitializeOnLoadMethodAttributes (68ms)
			AfterProcessingInitializeOnLoad (20ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.14 seconds
Refreshing native plugins compatible for Editor in 3.89 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 8 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3305 unused Assets / (0.9 MB). Loaded Objects now: 3794.
Memory consumption went from 79.6 MB to 78.7 MB.
Total: 15.694700 ms (FindLiveObjects: 1.478300 ms CreateObjectMapping: 1.060400 ms MarkObjects: 9.798100 ms  DeleteObjects: 3.353700 ms)

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  2.302 seconds
Refreshing native plugins compatible for Editor in 1.51 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.202 seconds
Domain Reload Profiling: 3506ms
	BeginReloadAssembly (835ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (44ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (326ms)
	RebuildCommonClasses (59ms)
	RebuildNativeTypeToScriptingClass (20ms)
	initialDomainReloadingComplete (52ms)
	LoadAllAssembliesAndSetupDomain (1336ms)
		LoadAssemblies (1209ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (419ms)
			TypeCache.Refresh (32ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (349ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (1203ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (921ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (19ms)
			BeforeProcessingInitializeOnLoad (291ms)
			ProcessInitializeOnLoadAttributes (564ms)
			ProcessInitializeOnLoadMethodAttributes (32ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 1.91 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3303 unused Assets / (0.6 MB). Loaded Objects now: 3794.
Memory consumption went from 78.7 MB to 78.1 MB.
Total: 9.891500 ms (FindLiveObjects: 0.870400 ms CreateObjectMapping: 0.310500 ms MarkObjects: 7.483600 ms  DeleteObjects: 1.223700 ms)

Prepare: number of updated asset objects reloaded= 0
