using System;
using System.Collections;
using System.Collections.Generic;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading;
using UnityEngine;
using Newtonsoft.Json;

[System.Serializable]
public class PoseFrameData
{
    public float timestamp;
    public int frame_id;
    public List<List<float>> landmarks;
    public List<float> bbox;
    public float confidence;
    public int person_id;
}

public class RealTimeReceiver : MonoBehaviour
{
    [Header("Connection Settings")]
    public int port = 12345;
    public bool autoStart = true;
    
    [Header("Animation Settings")]
    public GameObject[] bodyJoints;
    public float scaleFactor = 0.01f;
    public bool enableSmoothing = true;
    public float smoothingFactor = 0.1f;
    
    [Header("Debug")]
    public bool showDebugInfo = true;
    public UnityEngine.UI.Text debugText;
    
    private TcpListener tcpListener;
    private Thread tcpListenerThread;
    private bool isListening = false;
    private Queue<PoseFrameData> poseQueue = new Queue<PoseFrameData>();
    private readonly object queueLock = new object();
    
    // Smoothing
    private Vector3[] previousPositions;
    private Vector3[] targetPositions;
    
    // Statistics
    private int framesReceived = 0;
    private float lastFrameTime = 0f;
    private float averageFPS = 0f;
    
    void Start()
    {
        InitializePositionArrays();
        
        if (autoStart)
        {
            StartListening();
        }
    }
    
    void InitializePositionArrays()
    {
        if (bodyJoints != null && bodyJoints.Length > 0)
        {
            previousPositions = new Vector3[bodyJoints.Length];
            targetPositions = new Vector3[bodyJoints.Length];
            
            for (int i = 0; i < bodyJoints.Length; i++)
            {
                if (bodyJoints[i] != null)
                {
                    previousPositions[i] = bodyJoints[i].transform.localPosition;
                    targetPositions[i] = bodyJoints[i].transform.localPosition;
                }
            }
        }
    }
    
    public void StartListening()
    {
        if (isListening) return;
        
        try
        {
            tcpListener = new TcpListener(IPAddress.Any, port);
            tcpListenerThread = new Thread(new ThreadStart(ListenForClients));
            tcpListenerThread.IsBackground = true;
            tcpListenerThread.Start();
            
            isListening = true;
            Debug.Log($"Real-time receiver started on port {port}");
        }
        catch (Exception e)
        {
            Debug.LogError($"Failed to start listener: {e.Message}");
        }
    }
    
    public void StopListening()
    {
        if (!isListening) return;
        
        isListening = false;
        
        if (tcpListener != null)
        {
            tcpListener.Stop();
        }
        
        if (tcpListenerThread != null)
        {
            tcpListenerThread.Abort();
        }
        
        Debug.Log("Real-time receiver stopped");
    }
    
    private void ListenForClients()
    {
        tcpListener.Start();
        
        while (isListening)
        {
            try
            {
                using (TcpClient client = tcpListener.AcceptTcpClient())
                {
                    Debug.Log("Client connected");
                    ProcessClient(client);
                }
            }
            catch (Exception e)
            {
                if (isListening)
                {
                    Debug.LogError($"Error accepting client: {e.Message}");
                }
            }
        }
    }
    
    private void ProcessClient(TcpClient client)
    {
        NetworkStream clientStream = client.GetStream();
        byte[] lengthBuffer = new byte[4];
        
        while (client.Connected && isListening)
        {
            try
            {
                // Read message length
                int bytesRead = 0;
                while (bytesRead < 4)
                {
                    int read = clientStream.Read(lengthBuffer, bytesRead, 4 - bytesRead);
                    if (read == 0) return; // Client disconnected
                    bytesRead += read;
                }
                
                // Convert length from network byte order
                if (BitConverter.IsLittleEndian)
                {
                    Array.Reverse(lengthBuffer);
                }
                int messageLength = BitConverter.ToInt32(lengthBuffer, 0);
                
                // Read message data
                byte[] messageBuffer = new byte[messageLength];
                bytesRead = 0;
                while (bytesRead < messageLength)
                {
                    int read = clientStream.Read(messageBuffer, bytesRead, messageLength - bytesRead);
                    if (read == 0) return; // Client disconnected
                    bytesRead += read;
                }
                
                // Parse JSON message
                string jsonMessage = Encoding.UTF8.GetString(messageBuffer);
                PoseFrameData poseFrame = JsonConvert.DeserializeObject<PoseFrameData>(jsonMessage);
                
                // Add to queue for main thread processing
                lock (queueLock)
                {
                    poseQueue.Enqueue(poseFrame);
                    
                    // Limit queue size to prevent memory issues
                    while (poseQueue.Count > 10)
                    {
                        poseQueue.Dequeue();
                    }
                }
                
                framesReceived++;
            }
            catch (Exception e)
            {
                Debug.LogError($"Error processing client data: {e.Message}");
                break;
            }
        }
        
        Debug.Log("Client disconnected");
    }
    
    void Update()
    {
        ProcessPoseQueue();
        UpdateDebugInfo();
    }
    
    private void ProcessPoseQueue()
    {
        lock (queueLock)
        {
            while (poseQueue.Count > 0)
            {
                PoseFrameData poseFrame = poseQueue.Dequeue();
                ApplyPoseData(poseFrame);
            }
        }
    }
    
    private void ApplyPoseData(PoseFrameData poseFrame)
    {
        if (poseFrame.landmarks == null || bodyJoints == null) return;
        
        // Update target positions
        int jointCount = Mathf.Min(poseFrame.landmarks.Count, bodyJoints.Length);
        
        for (int i = 0; i < jointCount; i++)
        {
            if (bodyJoints[i] != null && poseFrame.landmarks[i].Count >= 3)
            {
                float x = poseFrame.landmarks[i][0] * scaleFactor;
                float y = poseFrame.landmarks[i][1] * scaleFactor;
                float z = poseFrame.landmarks[i][2] * scaleFactor;
                
                targetPositions[i] = new Vector3(x, y, z);
            }
        }
        
        // Apply positions with optional smoothing
        for (int i = 0; i < jointCount; i++)
        {
            if (bodyJoints[i] != null)
            {
                if (enableSmoothing)
                {
                    Vector3 currentPos = bodyJoints[i].transform.localPosition;
                    Vector3 smoothedPos = Vector3.Lerp(currentPos, targetPositions[i], smoothingFactor);
                    bodyJoints[i].transform.localPosition = smoothedPos;
                }
                else
                {
                    bodyJoints[i].transform.localPosition = targetPositions[i];
                }
            }
        }
        
        // Update FPS calculation
        float currentTime = Time.time;
        if (lastFrameTime > 0)
        {
            float deltaTime = currentTime - lastFrameTime;
            float currentFPS = 1f / deltaTime;
            averageFPS = Mathf.Lerp(averageFPS, currentFPS, 0.1f);
        }
        lastFrameTime = currentTime;
    }
    
    private void UpdateDebugInfo()
    {
        if (showDebugInfo && debugText != null)
        {
            string debugInfo = $"Real-time Receiver Status:\n";
            debugInfo += $"Listening: {isListening}\n";
            debugInfo += $"Port: {port}\n";
            debugInfo += $"Frames Received: {framesReceived}\n";
            debugInfo += $"Average FPS: {averageFPS:F1}\n";
            debugInfo += $"Queue Size: {poseQueue.Count}\n";
            debugInfo += $"Smoothing: {(enableSmoothing ? "Enabled" : "Disabled")}\n";
            
            debugText.text = debugInfo;
        }
    }
    
    void OnDestroy()
    {
        StopListening();
    }
    
    void OnApplicationQuit()
    {
        StopListening();
    }
    
    // Public methods for external control
    public void SetPort(int newPort)
    {
        if (!isListening)
        {
            port = newPort;
        }
        else
        {
            Debug.LogWarning("Cannot change port while listening. Stop listener first.");
        }
    }
    
    public void SetScaleFactor(float factor)
    {
        scaleFactor = factor;
    }
    
    public void SetSmoothing(bool enabled, float factor = 0.1f)
    {
        enableSmoothing = enabled;
        smoothingFactor = factor;
    }
    
    public bool IsListening()
    {
        return isListening;
    }
    
    public int GetFramesReceived()
    {
        return framesReceived;
    }
    
    public float GetAverageFPS()
    {
        return averageFPS;
    }
    
    public void ResetStatistics()
    {
        framesReceived = 0;
        averageFPS = 0f;
        lastFrameTime = 0f;
    }
}
