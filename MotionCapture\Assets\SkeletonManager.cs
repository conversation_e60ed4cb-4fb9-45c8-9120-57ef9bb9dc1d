using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class SkeletonManager : MonoBehaviour
{
    [Header("Skeleton Configuration")]
    public GameObject jointPrefab;
    public Material boneMaterial;
    public float jointScale = 0.1f;
    public float boneWidth = 0.02f;
    
    [Header("Colors")]
    public Color jointColor = Color.white;
    public Color boneColor = Color.cyan;
    public Color leftSideColor = Color.red;
    public Color rightSideColor = Color.blue;
    public Color centerColor = Color.green;
    
    [Header("Visibility")]
    public bool showJoints = true;
    public bool showBones = true;
    public bool colorCodeSides = true;
    
    private GameObject[] joints;
    private LineRenderer[] bones;
    private Transform skeletonRoot;
    
    // MediaPipe pose landmark indices and connections
    private readonly int[][] boneConnections = new int[][]
    {
        // Face outline
        new int[] {0, 1}, new int[] {1, 2}, new int[] {2, 3}, new int[] {3, 7},
        new int[] {0, 4}, new int[] {4, 5}, new int[] {5, 6}, new int[] {6, 8},
        
        // Torso
        new int[] {9, 10}, new int[] {11, 12}, new int[] {11, 23}, new int[] {12, 24}, new int[] {23, 24},
        
        // Left arm
        new int[] {11, 13}, new int[] {13, 15}, new int[] {15, 17}, new int[] {15, 19}, new int[] {15, 21}, new int[] {17, 19},
        
        // Right arm  
        new int[] {12, 14}, new int[] {14, 16}, new int[] {16, 18}, new int[] {16, 20}, new int[] {16, 22}, new int[] {18, 20},
        
        // Left leg
        new int[] {23, 25}, new int[] {25, 27}, new int[] {27, 29}, new int[] {29, 31}, new int[] {27, 31},
        
        // Right leg
        new int[] {24, 26}, new int[] {26, 28}, new int[] {28, 30}, new int[] {30, 32}, new int[] {28, 32}
    };
    
    // Joint names for reference
    private readonly string[] jointNames = new string[]
    {
        "nose", "left_eye_inner", "left_eye", "left_eye_outer", "right_eye_inner", "right_eye", "right_eye_outer",
        "left_ear", "right_ear", "mouth_left", "mouth_right", "left_shoulder", "right_shoulder", "left_elbow",
        "right_elbow", "left_wrist", "right_wrist", "left_pinky", "right_pinky", "left_index", "right_index",
        "left_thumb", "right_thumb", "left_hip", "right_hip", "left_knee", "right_knee", "left_ankle",
        "right_ankle", "left_heel", "right_heel", "left_foot_index", "right_foot_index"
    };
    
    void Start()
    {
        CreateSkeleton();
    }
    
    void CreateSkeleton()
    {
        // Create root object for skeleton
        skeletonRoot = new GameObject("SkeletonRoot").transform;
        skeletonRoot.SetParent(transform);
        
        // Create joints
        CreateJoints();
        
        // Create bones
        CreateBones();
        
        Debug.Log($"Skeleton created with {joints.Length} joints and {bones.Length} bones");
    }
    
    void CreateJoints()
    {
        joints = new GameObject[33]; // MediaPipe has 33 pose landmarks
        
        for (int i = 0; i < joints.Length; i++)
        {
            // Create joint object
            if (jointPrefab != null)
            {
                joints[i] = Instantiate(jointPrefab, skeletonRoot);
            }
            else
            {
                joints[i] = GameObject.CreatePrimitive(PrimitiveType.Sphere);
                joints[i].transform.SetParent(skeletonRoot);
            }
            
            joints[i].name = $"Joint_{i}_{jointNames[i]}";
            joints[i].transform.localScale = Vector3.one * jointScale;
            
            // Set color based on side
            Renderer renderer = joints[i].GetComponent<Renderer>();
            if (renderer != null)
            {
                Material mat = new Material(Shader.Find("Standard"));
                
                if (colorCodeSides)
                {
                    if (IsLeftSideJoint(i))
                        mat.color = leftSideColor;
                    else if (IsRightSideJoint(i))
                        mat.color = rightSideColor;
                    else
                        mat.color = centerColor;
                }
                else
                {
                    mat.color = jointColor;
                }
                
                renderer.material = mat;
            }
            
            // Add collider for interaction
            if (joints[i].GetComponent<Collider>() == null)
            {
                joints[i].AddComponent<SphereCollider>();
            }
        }
    }
    
    void CreateBones()
    {
        bones = new LineRenderer[boneConnections.Length];
        
        for (int i = 0; i < boneConnections.Length; i++)
        {
            // Create bone object
            GameObject boneObj = new GameObject($"Bone_{i}_{boneConnections[i][0]}_to_{boneConnections[i][1]}");
            boneObj.transform.SetParent(skeletonRoot);
            
            // Add LineRenderer
            LineRenderer lineRenderer = boneObj.AddComponent<LineRenderer>();
            bones[i] = lineRenderer;
            
            // Configure LineRenderer
            lineRenderer.positionCount = 2;
            lineRenderer.startWidth = boneWidth;
            lineRenderer.endWidth = boneWidth;
            lineRenderer.useWorldSpace = false;
            
            // Set material
            if (boneMaterial != null)
            {
                lineRenderer.material = boneMaterial;
            }
            else
            {
                Material mat = new Material(Shader.Find("Sprites/Default"));
                mat.color = boneColor;
                lineRenderer.material = mat;
            }
            
            // Set color based on connection
            if (colorCodeSides)
            {
                int joint1 = boneConnections[i][0];
                int joint2 = boneConnections[i][1];
                
                Color connectionColor = boneColor;
                if (IsLeftSideJoint(joint1) && IsLeftSideJoint(joint2))
                    connectionColor = leftSideColor;
                else if (IsRightSideJoint(joint1) && IsRightSideJoint(joint2))
                    connectionColor = rightSideColor;
                else if (IsCenterJoint(joint1) && IsCenterJoint(joint2))
                    connectionColor = centerColor;
                
                lineRenderer.color = connectionColor;
            }
        }
    }
    
    bool IsLeftSideJoint(int jointIndex)
    {
        // Left side joints (from person's perspective)
        int[] leftJoints = {1, 2, 3, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31};
        return System.Array.IndexOf(leftJoints, jointIndex) >= 0;
    }
    
    bool IsRightSideJoint(int jointIndex)
    {
        // Right side joints (from person's perspective)
        int[] rightJoints = {4, 5, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32};
        return System.Array.IndexOf(rightJoints, jointIndex) >= 0;
    }
    
    bool IsCenterJoint(int jointIndex)
    {
        return !IsLeftSideJoint(jointIndex) && !IsRightSideJoint(jointIndex);
    }
    
    void Update()
    {
        UpdateBonePositions();
        UpdateVisibility();
    }
    
    void UpdateBonePositions()
    {
        if (bones == null || joints == null) return;
        
        for (int i = 0; i < bones.Length; i++)
        {
            if (bones[i] != null && boneConnections[i].Length >= 2)
            {
                int joint1Index = boneConnections[i][0];
                int joint2Index = boneConnections[i][1];
                
                if (joint1Index < joints.Length && joint2Index < joints.Length &&
                    joints[joint1Index] != null && joints[joint2Index] != null)
                {
                    bones[i].SetPosition(0, joints[joint1Index].transform.position);
                    bones[i].SetPosition(1, joints[joint2Index].transform.position);
                }
            }
        }
    }
    
    void UpdateVisibility()
    {
        // Update joint visibility
        if (joints != null)
        {
            foreach (GameObject joint in joints)
            {
                if (joint != null)
                {
                    joint.SetActive(showJoints);
                }
            }
        }
        
        // Update bone visibility
        if (bones != null)
        {
            foreach (LineRenderer bone in bones)
            {
                if (bone != null)
                {
                    bone.enabled = showBones;
                }
            }
        }
    }
    
    // Public methods for external control
    public GameObject[] GetJoints()
    {
        return joints;
    }
    
    public LineRenderer[] GetBones()
    {
        return bones;
    }
    
    public void SetJointScale(float scale)
    {
        jointScale = scale;
        if (joints != null)
        {
            foreach (GameObject joint in joints)
            {
                if (joint != null)
                {
                    joint.transform.localScale = Vector3.one * scale;
                }
            }
        }
    }
    
    public void SetBoneWidth(float width)
    {
        boneWidth = width;
        if (bones != null)
        {
            foreach (LineRenderer bone in bones)
            {
                if (bone != null)
                {
                    bone.startWidth = width;
                    bone.endWidth = width;
                }
            }
        }
    }
    
    public void SetJointVisibility(bool visible)
    {
        showJoints = visible;
    }
    
    public void SetBoneVisibility(bool visible)
    {
        showBones = visible;
    }
    
    public void SetColorCoding(bool enabled)
    {
        colorCodeSides = enabled;
        // Recreate skeleton with new colors
        if (Application.isPlaying)
        {
            DestroySkeleton();
            CreateSkeleton();
        }
    }
    
    public void DestroySkeleton()
    {
        if (skeletonRoot != null)
        {
            DestroyImmediate(skeletonRoot.gameObject);
        }
        joints = null;
        bones = null;
    }
    
    void OnDestroy()
    {
        DestroySkeleton();
    }
    
    // Get joint by name
    public GameObject GetJointByName(string name)
    {
        for (int i = 0; i < jointNames.Length; i++)
        {
            if (jointNames[i] == name && i < joints.Length)
            {
                return joints[i];
            }
        }
        return null;
    }
    
    // Get joint index by name
    public int GetJointIndexByName(string name)
    {
        for (int i = 0; i < jointNames.Length; i++)
        {
            if (jointNames[i] == name)
            {
                return i;
            }
        }
        return -1;
    }
}
