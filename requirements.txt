# Core dependencies
opencv-python>=4.8.0
cvzone>=1.5.6
mediapipe>=0.10.0
numpy>=1.21.0

# Data processing and analysis
pandas>=1.3.0
matplotlib>=3.5.0
scipy>=1.7.0

# GUI and interface
tkinter  # Usually comes with Python
Pillow>=8.3.0

# JSON processing (usually built-in)
# json

# Networking and communication
# socket (built-in)
# threading (built-in)

# Optional: For advanced features
# scikit-learn>=1.0.0  # For machine learning features
# tensorflow>=2.8.0    # For custom pose models
# pytorch>=1.11.0      # Alternative ML framework

# Development and testing
pytest>=6.2.0
pytest-cov>=2.12.0

# Documentation
sphinx>=4.0.0
sphinx-rtd-theme>=0.5.0

# Linting and formatting
flake8>=3.9.0
black>=21.0.0
isort>=5.9.0

# Type checking
mypy>=0.910
