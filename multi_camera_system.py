"""
Multi-Camera Motion Capture System
Supports multiple cameras for improved 3D pose estimation through triangulation
"""

import cv2
import numpy as np
import json
import time
import threading
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
import logging

from advanced_pose_detector import AdvancedPoseDetector, DetectedPose

logger = logging.getLogger(__name__)

@dataclass
class CameraCalibration:
    """Camera calibration parameters"""
    camera_id: int
    camera_matrix: np.ndarray
    distortion_coeffs: np.ndarray
    rotation_vector: np.ndarray
    translation_vector: np.ndarray
    image_size: Tuple[int, int]
    
    def to_dict(self):
        """Convert to dictionary for JSON serialization"""
        return {
            'camera_id': self.camera_id,
            'camera_matrix': self.camera_matrix.tolist(),
            'distortion_coeffs': self.distortion_coeffs.tolist(),
            'rotation_vector': self.rotation_vector.tolist(),
            'translation_vector': self.translation_vector.tolist(),
            'image_size': self.image_size
        }
    
    @classmethod
    def from_dict(cls, data):
        """Create from dictionary"""
        return cls(
            camera_id=data['camera_id'],
            camera_matrix=np.array(data['camera_matrix']),
            distortion_coeffs=np.array(data['distortion_coeffs']),
            rotation_vector=np.array(data['rotation_vector']),
            translation_vector=np.array(data['translation_vector']),
            image_size=tuple(data['image_size'])
        )

@dataclass
class Pose3D:
    """3D pose with triangulated landmarks"""
    person_id: int
    landmarks_3d: List[List[float]]  # [x, y, z, confidence]
    timestamp: float
    source_cameras: List[int]
    triangulation_error: float

class CameraManager:
    """Manages multiple cameras"""
    
    def __init__(self, camera_ids: List[int]):
        self.camera_ids = camera_ids
        self.cameras = {}
        self.calibrations = {}
        self.pose_detectors = {}
        
        # Threading
        self.capture_threads = {}
        self.frame_queues = {}
        self.running = False
        
        # Synchronization
        self.frame_sync_tolerance = 0.1  # seconds
        self.synchronized_frames = {}
        
    def initialize_cameras(self) -> bool:
        """Initialize all cameras"""
        logger.info(f"Initializing {len(self.camera_ids)} cameras...")
        
        for camera_id in self.camera_ids:
            try:
                cap = cv2.VideoCapture(camera_id)
                if not cap.isOpened():
                    logger.error(f"Failed to open camera {camera_id}")
                    continue
                
                # Set camera properties
                cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
                cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
                cap.set(cv2.CAP_PROP_FPS, 30)
                
                self.cameras[camera_id] = cap
                self.pose_detectors[camera_id] = AdvancedPoseDetector(max_num_poses=1)
                
                logger.info(f"Camera {camera_id} initialized successfully")
                
            except Exception as e:
                logger.error(f"Error initializing camera {camera_id}: {e}")
        
        return len(self.cameras) > 0
    
    def load_calibrations(self, calibration_file: str) -> bool:
        """Load camera calibrations from file"""
        try:
            with open(calibration_file, 'r') as f:
                calibration_data = json.load(f)
            
            for camera_id_str, calib_data in calibration_data.items():
                camera_id = int(camera_id_str)
                if camera_id in self.camera_ids:
                    self.calibrations[camera_id] = CameraCalibration.from_dict(calib_data)
                    logger.info(f"Loaded calibration for camera {camera_id}")
            
            return len(self.calibrations) > 0
            
        except Exception as e:
            logger.error(f"Error loading calibrations: {e}")
            return False
    
    def calibrate_cameras(self, calibration_images_dir: str) -> bool:
        """Calibrate cameras using checkerboard patterns"""
        logger.info("Starting camera calibration...")
        
        # Checkerboard parameters
        pattern_size = (9, 6)  # Internal corners
        square_size = 25.0  # mm
        
        # Prepare object points
        objp = np.zeros((pattern_size[0] * pattern_size[1], 3), np.float32)
        objp[:, :2] = np.mgrid[0:pattern_size[0], 0:pattern_size[1]].T.reshape(-1, 2)
        objp *= square_size
        
        calibrations = {}
        
        for camera_id in self.camera_ids:
            if camera_id not in self.cameras:
                continue
            
            logger.info(f"Calibrating camera {camera_id}...")
            
            # Collect calibration images
            objpoints = []  # 3D points
            imgpoints = []  # 2D points
            
            cap = self.cameras[camera_id]
            image_count = 0
            
            while image_count < 20:  # Collect 20 calibration images
                ret, frame = cap.read()
                if not ret:
                    continue
                
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                
                # Find checkerboard corners
                ret, corners = cv2.findChessboardCorners(gray, pattern_size, None)
                
                if ret:
                    objpoints.append(objp)
                    
                    # Refine corner positions
                    corners2 = cv2.cornerSubPix(gray, corners, (11, 11), (-1, -1),
                                              (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 0.001))
                    imgpoints.append(corners2)
                    
                    # Draw corners
                    cv2.drawChessboardCorners(frame, pattern_size, corners2, ret)
                    image_count += 1
                    
                    logger.info(f"Camera {camera_id}: Captured {image_count}/20 calibration images")
                
                cv2.imshow(f'Calibration Camera {camera_id}', frame)
                if cv2.waitKey(1) & 0xFF == ord('q'):
                    break
            
            cv2.destroyAllWindows()
            
            if len(objpoints) < 10:
                logger.error(f"Insufficient calibration images for camera {camera_id}")
                continue
            
            # Calibrate camera
            ret, camera_matrix, dist_coeffs, rvecs, tvecs = cv2.calibrateCamera(
                objpoints, imgpoints, gray.shape[::-1], None, None)
            
            if ret:
                calibrations[camera_id] = CameraCalibration(
                    camera_id=camera_id,
                    camera_matrix=camera_matrix,
                    distortion_coeffs=dist_coeffs,
                    rotation_vector=rvecs[0] if rvecs else np.zeros(3),
                    translation_vector=tvecs[0] if tvecs else np.zeros(3),
                    image_size=gray.shape[::-1]
                )
                
                logger.info(f"Camera {camera_id} calibrated successfully")
            else:
                logger.error(f"Calibration failed for camera {camera_id}")
        
        self.calibrations = calibrations
        
        # Save calibrations
        self.save_calibrations("camera_calibrations.json")
        
        return len(calibrations) > 0
    
    def save_calibrations(self, filename: str):
        """Save calibrations to file"""
        try:
            calibration_data = {}
            for camera_id, calib in self.calibrations.items():
                calibration_data[str(camera_id)] = calib.to_dict()
            
            with open(filename, 'w') as f:
                json.dump(calibration_data, f, indent=2)
            
            logger.info(f"Calibrations saved to {filename}")
            
        except Exception as e:
            logger.error(f"Error saving calibrations: {e}")
    
    def start_capture(self):
        """Start capturing from all cameras"""
        self.running = True
        
        for camera_id in self.cameras.keys():
            thread = threading.Thread(target=self._capture_loop, args=(camera_id,))
            thread.daemon = True
            thread.start()
            self.capture_threads[camera_id] = thread
        
        logger.info("Started capture from all cameras")
    
    def stop_capture(self):
        """Stop capturing from all cameras"""
        self.running = False
        
        for thread in self.capture_threads.values():
            thread.join(timeout=1.0)
        
        logger.info("Stopped capture from all cameras")
    
    def _capture_loop(self, camera_id: int):
        """Capture loop for individual camera"""
        cap = self.cameras[camera_id]
        detector = self.pose_detectors[camera_id]
        
        while self.running:
            try:
                ret, frame = cap.read()
                if not ret:
                    continue
                
                timestamp = time.time()
                
                # Detect poses
                poses = detector.detect_poses(frame)
                
                # Store synchronized frame data
                frame_data = {
                    'timestamp': timestamp,
                    'frame': frame,
                    'poses': poses
                }
                
                self.synchronized_frames[camera_id] = frame_data
                
            except Exception as e:
                logger.error(f"Error in capture loop for camera {camera_id}: {e}")
    
    def get_synchronized_frames(self) -> Dict[int, Dict]:
        """Get synchronized frames from all cameras"""
        if not self.synchronized_frames:
            return {}
        
        # Find reference timestamp (most recent)
        ref_timestamp = max(data['timestamp'] for data in self.synchronized_frames.values())
        
        # Filter frames within sync tolerance
        synced_frames = {}
        for camera_id, frame_data in self.synchronized_frames.items():
            time_diff = abs(frame_data['timestamp'] - ref_timestamp)
            if time_diff <= self.frame_sync_tolerance:
                synced_frames[camera_id] = frame_data
        
        return synced_frames
    
    def cleanup(self):
        """Clean up resources"""
        self.stop_capture()
        
        for cap in self.cameras.values():
            cap.release()
        
        for detector in self.pose_detectors.values():
            detector.close()
        
        cv2.destroyAllWindows()

class MultiCameraPoseEstimator:
    """3D pose estimation using multiple cameras"""
    
    def __init__(self, camera_manager: CameraManager):
        self.camera_manager = camera_manager
        self.triangulation_threshold = 0.5  # Minimum confidence for triangulation
        
    def triangulate_3d_pose(self, camera_poses: Dict[int, List[DetectedPose]]) -> List[Pose3D]:
        """Triangulate 3D poses from multiple camera views"""
        if len(camera_poses) < 2:
            return []
        
        poses_3d = []
        
        # Group poses by person ID across cameras
        person_groups = self._group_poses_by_person(camera_poses)
        
        for person_id, camera_pose_dict in person_groups.items():
            if len(camera_pose_dict) < 2:
                continue  # Need at least 2 cameras for triangulation
            
            # Triangulate landmarks
            landmarks_3d = self._triangulate_landmarks(camera_pose_dict)
            
            if landmarks_3d:
                pose_3d = Pose3D(
                    person_id=person_id,
                    landmarks_3d=landmarks_3d,
                    timestamp=time.time(),
                    source_cameras=list(camera_pose_dict.keys()),
                    triangulation_error=self._calculate_triangulation_error(camera_pose_dict, landmarks_3d)
                )
                poses_3d.append(pose_3d)
        
        return poses_3d
    
    def _group_poses_by_person(self, camera_poses: Dict[int, List[DetectedPose]]) -> Dict[int, Dict[int, DetectedPose]]:
        """Group poses by person ID across cameras"""
        person_groups = {}
        
        # Simple approach: match poses by position similarity
        # In a real system, you'd use more sophisticated tracking
        
        for camera_id, poses in camera_poses.items():
            for pose in poses:
                # For now, assume person_id 0 from each camera refers to the same person
                # In practice, you'd need cross-camera person matching
                if pose.person_id not in person_groups:
                    person_groups[pose.person_id] = {}
                
                person_groups[pose.person_id][camera_id] = pose
        
        return person_groups
    
    def _triangulate_landmarks(self, camera_pose_dict: Dict[int, DetectedPose]) -> List[List[float]]:
        """Triangulate 3D landmarks from 2D detections"""
        landmarks_3d = []
        
        # Get calibrations for involved cameras
        calibrations = {}
        for camera_id in camera_pose_dict.keys():
            if camera_id in self.camera_manager.calibrations:
                calibrations[camera_id] = self.camera_manager.calibrations[camera_id]
        
        if len(calibrations) < 2:
            return []
        
        # Triangulate each landmark
        num_landmarks = len(next(iter(camera_pose_dict.values())).landmarks)
        
        for landmark_idx in range(num_landmarks):
            # Collect 2D points from all cameras
            points_2d = []
            projection_matrices = []
            confidences = []
            
            for camera_id, pose in camera_pose_dict.items():
                if camera_id not in calibrations:
                    continue
                
                landmark = pose.landmarks[landmark_idx]
                if len(landmark) >= 4 and landmark[3] > self.triangulation_threshold:
                    calib = calibrations[camera_id]
                    
                    # Undistort point
                    point_2d = np.array([[landmark[0], landmark[1]]], dtype=np.float32)
                    undistorted = cv2.undistortPoints(point_2d, calib.camera_matrix, calib.distortion_coeffs)
                    
                    points_2d.append(undistorted[0][0])
                    
                    # Create projection matrix
                    R, _ = cv2.Rodrigues(calib.rotation_vector)
                    P = calib.camera_matrix @ np.hstack([R, calib.translation_vector.reshape(-1, 1)])
                    projection_matrices.append(P)
                    
                    confidences.append(landmark[3])
            
            if len(points_2d) >= 2:
                # Triangulate using DLT (Direct Linear Transform)
                point_3d = self._triangulate_dlt(points_2d, projection_matrices)
                
                # Calculate average confidence
                avg_confidence = np.mean(confidences)
                
                landmarks_3d.append([point_3d[0], point_3d[1], point_3d[2], avg_confidence])
            else:
                # No valid points for triangulation
                landmarks_3d.append([0.0, 0.0, 0.0, 0.0])
        
        return landmarks_3d
    
    def _triangulate_dlt(self, points_2d: List[np.ndarray], projection_matrices: List[np.ndarray]) -> np.ndarray:
        """Triangulate 3D point using Direct Linear Transform"""
        A = []
        
        for i, (point_2d, P) in enumerate(zip(points_2d, projection_matrices)):
            x, y = point_2d
            
            A.append(x * P[2] - P[0])
            A.append(y * P[2] - P[1])
        
        A = np.array(A)
        
        # Solve using SVD
        _, _, Vt = np.linalg.svd(A)
        point_3d_homogeneous = Vt[-1]
        
        # Convert from homogeneous coordinates
        point_3d = point_3d_homogeneous[:3] / point_3d_homogeneous[3]
        
        return point_3d
    
    def _calculate_triangulation_error(self, camera_pose_dict: Dict[int, DetectedPose], landmarks_3d: List[List[float]]) -> float:
        """Calculate reprojection error for triangulated points"""
        total_error = 0.0
        point_count = 0
        
        for camera_id, pose in camera_pose_dict.items():
            if camera_id not in self.camera_manager.calibrations:
                continue
            
            calib = self.camera_manager.calibrations[camera_id]
            
            for i, (landmark_2d, landmark_3d) in enumerate(zip(pose.landmarks, landmarks_3d)):
                if landmark_2d[3] > self.triangulation_threshold and landmark_3d[3] > 0:
                    # Project 3D point back to 2D
                    point_3d = np.array([landmark_3d[:3]], dtype=np.float32)
                    
                    projected_2d, _ = cv2.projectPoints(
                        point_3d,
                        calib.rotation_vector,
                        calib.translation_vector,
                        calib.camera_matrix,
                        calib.distortion_coeffs
                    )
                    
                    # Calculate reprojection error
                    error = np.linalg.norm(projected_2d[0][0] - np.array([landmark_2d[0], landmark_2d[1]]))
                    total_error += error
                    point_count += 1
        
        return total_error / point_count if point_count > 0 else float('inf')

# Example usage
def main():
    """Example usage of multi-camera system"""
    # Initialize camera manager
    camera_ids = [0, 1]  # Use cameras 0 and 1
    camera_manager = CameraManager(camera_ids)
    
    if not camera_manager.initialize_cameras():
        print("Failed to initialize cameras")
        return
    
    # Load or perform calibration
    if not camera_manager.load_calibrations("camera_calibrations.json"):
        print("No calibrations found. Starting calibration process...")
        if not camera_manager.calibrate_cameras("calibration_images"):
            print("Calibration failed")
            return
    
    # Initialize pose estimator
    pose_estimator = MultiCameraPoseEstimator(camera_manager)
    
    # Start capture
    camera_manager.start_capture()
    
    try:
        while True:
            # Get synchronized frames
            synced_frames = camera_manager.get_synchronized_frames()
            
            if len(synced_frames) >= 2:
                # Extract poses from each camera
                camera_poses = {}
                for camera_id, frame_data in synced_frames.items():
                    camera_poses[camera_id] = frame_data['poses']
                
                # Triangulate 3D poses
                poses_3d = pose_estimator.triangulate_3d_pose(camera_poses)
                
                # Display results
                for pose_3d in poses_3d:
                    print(f"Person {pose_3d.person_id}: {len(pose_3d.landmarks_3d)} 3D landmarks")
                    print(f"Triangulation error: {pose_3d.triangulation_error:.2f}")
                
                # Display camera views
                for camera_id, frame_data in synced_frames.items():
                    frame = frame_data['frame']
                    cv2.imshow(f'Camera {camera_id}', frame)
            
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
    
    finally:
        camera_manager.cleanup()

if __name__ == "__main__":
    main()
