# Add a node to a Script Graph

All logic in a Script Graph starts with a node. 

> [!TIP]
> Depending on the method you used to create your Script Graph, you might already have two Event nodes in your graph: Start and Update. For more information on these nodes, see [Events node](vs-events-reference.md).

To add a node to a Script Graph: 

1. [!include[vs-with-graph-open-ff](./snippets/vs-with-graph-open-ff.md)]

2. In the fuzzy finder, enter a search term into the Search bar or select a category from the list to find related nodes. 
  Categories have an arrow (>) at the end of their entry in the fuzzy finder. 

3. Select an entry in the fuzzy finder to add that node to your Script Graph. 
  Visual Scripting adds the node to your Script Graph at the location where you opened the fuzzy finder. 


![An image of a node added to a Script Graph](images/vs-node-example.png)

## Next steps

After you've added a node to a graph, you can add additional nodes and [connect nodes in a Script Graph](vs-creating-connections.md) to create logic for your application.

You can also add a [Sticky Note](vs-sticky-notes.md) to add comments to a graph.
