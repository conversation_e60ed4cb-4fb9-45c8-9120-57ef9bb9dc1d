"""
Data Processing Utilities for Motion Capture System
Handles data conversion, filtering, and analysis
"""

import json
import numpy as np
import matplotlib.pyplot as plt
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
import cv2
from pathlib import Path
import pandas as pd

@dataclass
class MotionAnalytics:
    """Analytics data for motion capture session"""
    total_frames: int
    average_confidence: float
    motion_intensity: float
    key_poses: List[int]
    smoothness_score: float
    tracking_quality: float

class DataProcessor:
    """Processes and analyzes motion capture data"""
    
    # MediaPipe pose landmark connections
    POSE_CONNECTIONS = [
        # Face
        (0, 1), (1, 2), (2, 3), (3, 7),
        (0, 4), (4, 5), (5, 6), (6, 8),
        # Torso
        (9, 10), (11, 12), (11, 13), (13, 15),
        (12, 14), (14, 16), (11, 23), (12, 24),
        (23, 24),
        # Left arm
        (11, 13), (13, 15), (15, 17), (15, 19),
        (15, 21), (17, 19),
        # Right arm
        (12, 14), (14, 16), (16, 18), (16, 20),
        (16, 22), (18, 20),
        # Left leg
        (23, 25), (25, 27), (27, 29), (29, 31),
        (27, 31),
        # Right leg
        (24, 26), (26, 28), (28, 30), (30, 32),
        (28, 32)
    ]
    
    def __init__(self):
        self.pose_data: List[Dict] = []
        self.analytics: Optional[MotionAnalytics] = None
    
    def load_json_data(self, file_path: str) -> bool:
        """Load pose data from JSON file"""
        try:
            with open(file_path, 'r') as f:
                self.pose_data = json.load(f)
            print(f"Loaded {len(self.pose_data)} frames from {file_path}")
            return True
        except Exception as e:
            print(f"Error loading JSON data: {e}")
            return False
    
    def load_txt_data(self, file_path: str) -> bool:
        """Load pose data from text file and convert to structured format"""
        try:
            pose_frames = []
            with open(file_path, 'r') as f:
                for frame_id, line in enumerate(f):
                    if line.strip():
                        coords = [float(x) for x in line.strip().split(',') if x]
                        
                        # Reshape into landmarks (assuming 33 landmarks with x,y,z)
                        landmarks = []
                        for i in range(0, len(coords), 3):
                            if i + 2 < len(coords):
                                landmarks.append([coords[i], coords[i+1], coords[i+2], 1.0])
                        
                        frame_data = {
                            'frame_id': frame_id,
                            'timestamp': frame_id / 30.0,  # Assume 30 FPS
                            'landmarks': landmarks,
                            'confidence': 1.0
                        }
                        pose_frames.append(frame_data)
            
            self.pose_data = pose_frames
            print(f"Loaded {len(self.pose_data)} frames from {file_path}")
            return True
        except Exception as e:
            print(f"Error loading text data: {e}")
            return False
    
    def calculate_motion_intensity(self) -> float:
        """Calculate overall motion intensity"""
        if len(self.pose_data) < 2:
            return 0.0
        
        total_movement = 0.0
        frame_count = 0
        
        for i in range(1, len(self.pose_data)):
            prev_frame = self.pose_data[i-1]
            curr_frame = self.pose_data[i]
            
            if 'landmarks' in prev_frame and 'landmarks' in curr_frame:
                frame_movement = 0.0
                
                for j, (prev_lm, curr_lm) in enumerate(zip(prev_frame['landmarks'], curr_frame['landmarks'])):
                    if len(prev_lm) >= 3 and len(curr_lm) >= 3:
                        dx = curr_lm[0] - prev_lm[0]
                        dy = curr_lm[1] - prev_lm[1]
                        dz = curr_lm[2] - prev_lm[2]
                        movement = np.sqrt(dx*dx + dy*dy + dz*dz)
                        frame_movement += movement
                
                total_movement += frame_movement
                frame_count += 1
        
        return total_movement / frame_count if frame_count > 0 else 0.0
    
    def detect_key_poses(self, threshold: float = 0.1) -> List[int]:
        """Detect frames with significant pose changes"""
        key_frames = []
        
        if len(self.pose_data) < 3:
            return key_frames
        
        for i in range(1, len(self.pose_data) - 1):
            prev_frame = self.pose_data[i-1]
            curr_frame = self.pose_data[i]
            next_frame = self.pose_data[i+1]
            
            # Calculate movement from previous and to next frame
            prev_movement = self._calculate_frame_movement(prev_frame, curr_frame)
            next_movement = self._calculate_frame_movement(curr_frame, next_frame)
            
            # Detect peaks in movement
            if prev_movement > threshold and next_movement > threshold:
                key_frames.append(i)
        
        return key_frames
    
    def _calculate_frame_movement(self, frame1: Dict, frame2: Dict) -> float:
        """Calculate movement between two frames"""
        if 'landmarks' not in frame1 or 'landmarks' not in frame2:
            return 0.0
        
        total_movement = 0.0
        count = 0
        
        for lm1, lm2 in zip(frame1['landmarks'], frame2['landmarks']):
            if len(lm1) >= 3 and len(lm2) >= 3:
                dx = lm2[0] - lm1[0]
                dy = lm2[1] - lm1[1]
                dz = lm2[2] - lm1[2]
                movement = np.sqrt(dx*dx + dy*dy + dz*dz)
                total_movement += movement
                count += 1
        
        return total_movement / count if count > 0 else 0.0
    
    def calculate_smoothness_score(self) -> float:
        """Calculate how smooth the motion is (lower is smoother)"""
        if len(self.pose_data) < 3:
            return 0.0
        
        total_jerk = 0.0
        frame_count = 0
        
        for i in range(2, len(self.pose_data)):
            frame1 = self.pose_data[i-2]
            frame2 = self.pose_data[i-1]
            frame3 = self.pose_data[i]
            
            if all('landmarks' in f for f in [frame1, frame2, frame3]):
                frame_jerk = 0.0
                
                for j in range(min(len(frame1['landmarks']), len(frame2['landmarks']), len(frame3['landmarks']))):
                    lm1, lm2, lm3 = frame1['landmarks'][j], frame2['landmarks'][j], frame3['landmarks'][j]
                    
                    if all(len(lm) >= 3 for lm in [lm1, lm2, lm3]):
                        # Calculate acceleration (second derivative)
                        acc_x = lm3[0] - 2*lm2[0] + lm1[0]
                        acc_y = lm3[1] - 2*lm2[1] + lm1[1]
                        acc_z = lm3[2] - 2*lm2[2] + lm1[2]
                        
                        jerk = np.sqrt(acc_x*acc_x + acc_y*acc_y + acc_z*acc_z)
                        frame_jerk += jerk
                
                total_jerk += frame_jerk
                frame_count += 1
        
        return total_jerk / frame_count if frame_count > 0 else 0.0
    
    def generate_analytics(self) -> MotionAnalytics:
        """Generate comprehensive analytics for the motion data"""
        if not self.pose_data:
            return MotionAnalytics(0, 0.0, 0.0, [], 0.0, 0.0)
        
        # Calculate average confidence
        total_confidence = 0.0
        confidence_count = 0
        
        for frame in self.pose_data:
            if 'confidence' in frame:
                total_confidence += frame['confidence']
                confidence_count += 1
        
        avg_confidence = total_confidence / confidence_count if confidence_count > 0 else 0.0
        
        # Calculate other metrics
        motion_intensity = self.calculate_motion_intensity()
        key_poses = self.detect_key_poses()
        smoothness = self.calculate_smoothness_score()
        
        # Tracking quality (based on confidence and continuity)
        tracking_quality = min(avg_confidence, 1.0 - (smoothness / 10.0))
        
        self.analytics = MotionAnalytics(
            total_frames=len(self.pose_data),
            average_confidence=avg_confidence,
            motion_intensity=motion_intensity,
            key_poses=key_poses,
            smoothness_score=smoothness,
            tracking_quality=max(0.0, tracking_quality)
        )
        
        return self.analytics
    
    def export_to_bvh(self, output_path: str) -> bool:
        """Export motion data to BVH format (basic implementation)"""
        try:
            with open(output_path, 'w') as f:
                # Write BVH header
                f.write("HIERARCHY\n")
                f.write("ROOT Hips\n")
                f.write("{\n")
                f.write("  OFFSET 0.0 0.0 0.0\n")
                f.write("  CHANNELS 6 Xposition Yposition Zposition Zrotation Xrotation Yrotation\n")
                
                # Add basic skeleton structure
                joints = [
                    ("Chest", "Hips"), ("Neck", "Chest"), ("Head", "Neck"),
                    ("LeftShoulder", "Chest"), ("LeftArm", "LeftShoulder"), ("LeftForeArm", "LeftArm"),
                    ("RightShoulder", "Chest"), ("RightArm", "RightShoulder"), ("RightForeArm", "RightArm"),
                    ("LeftUpLeg", "Hips"), ("LeftLeg", "LeftUpLeg"), ("LeftFoot", "LeftLeg"),
                    ("RightUpLeg", "Hips"), ("RightLeg", "RightUpLeg"), ("RightFoot", "RightLeg")
                ]
                
                # Write joint hierarchy (simplified)
                for joint, parent in joints:
                    f.write(f"  JOINT {joint}\n")
                    f.write("  {\n")
                    f.write("    OFFSET 0.0 0.0 0.0\n")
                    f.write("    CHANNELS 3 Zrotation Xrotation Yrotation\n")
                    f.write("  }\n")
                
                f.write("}\n")
                
                # Write motion data
                f.write("MOTION\n")
                f.write(f"Frames: {len(self.pose_data)}\n")
                f.write("Frame Time: 0.033333\n")  # 30 FPS
                
                # Write frame data (simplified - just positions)
                for frame in self.pose_data:
                    if 'landmarks' in frame and len(frame['landmarks']) > 0:
                        # Use hip position as root
                        hip_pos = frame['landmarks'][23] if len(frame['landmarks']) > 23 else [0, 0, 0]
                        f.write(f"{hip_pos[0]} {hip_pos[1]} {hip_pos[2]} 0 0 0")
                        
                        # Add dummy rotations for other joints
                        for _ in range(len(joints)):
                            f.write(" 0 0 0")
                        f.write("\n")
            
            print(f"Exported BVH data to {output_path}")
            return True
            
        except Exception as e:
            print(f"Error exporting BVH: {e}")
            return False
    
    def create_motion_report(self, output_path: str = "motion_report.html") -> bool:
        """Create an HTML report with motion analysis"""
        if not self.analytics:
            self.generate_analytics()
        
        try:
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>Motion Capture Analysis Report</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 40px; }}
                    .metric {{ background: #f0f0f0; padding: 15px; margin: 10px 0; border-radius: 5px; }}
                    .header {{ color: #333; border-bottom: 2px solid #333; }}
                </style>
            </head>
            <body>
                <h1 class="header">Motion Capture Analysis Report</h1>
                
                <div class="metric">
                    <h3>Session Overview</h3>
                    <p><strong>Total Frames:</strong> {self.analytics.total_frames}</p>
                    <p><strong>Duration:</strong> {self.analytics.total_frames / 30.0:.2f} seconds</p>
                </div>
                
                <div class="metric">
                    <h3>Quality Metrics</h3>
                    <p><strong>Average Confidence:</strong> {self.analytics.average_confidence:.2%}</p>
                    <p><strong>Tracking Quality:</strong> {self.analytics.tracking_quality:.2%}</p>
                    <p><strong>Smoothness Score:</strong> {self.analytics.smoothness_score:.4f} (lower is better)</p>
                </div>
                
                <div class="metric">
                    <h3>Motion Analysis</h3>
                    <p><strong>Motion Intensity:</strong> {self.analytics.motion_intensity:.4f}</p>
                    <p><strong>Key Poses Detected:</strong> {len(self.analytics.key_poses)}</p>
                    <p><strong>Key Pose Frames:</strong> {', '.join(map(str, self.analytics.key_poses[:10]))}</p>
                </div>
                
                <div class="metric">
                    <h3>Recommendations</h3>
                    <ul>
                        {'<li>Good tracking quality!</li>' if self.analytics.tracking_quality > 0.8 else '<li>Consider improving lighting or camera position</li>'}
                        {'<li>Smooth motion detected</li>' if self.analytics.smoothness_score < 0.1 else '<li>Motion could be smoother - check for occlusions</li>'}
                        {'<li>High motion intensity - good for dynamic scenes</li>' if self.analytics.motion_intensity > 0.5 else '<li>Low motion intensity - suitable for subtle movements</li>'}
                    </ul>
                </div>
            </body>
            </html>
            """
            
            with open(output_path, 'w') as f:
                f.write(html_content)
            
            print(f"Motion analysis report saved to {output_path}")
            return True
            
        except Exception as e:
            print(f"Error creating report: {e}")
            return False

def main():
    """Example usage of DataProcessor"""
    processor = DataProcessor()
    
    # Try to load data
    if Path("motion_data.json").exists():
        processor.load_json_data("motion_data.json")
    elif Path("AnimationFile.txt").exists():
        processor.load_txt_data("AnimationFile.txt")
    else:
        print("No motion data found. Please run motion capture first.")
        return
    
    # Generate analytics
    analytics = processor.generate_analytics()
    
    print("\nMotion Analysis Results:")
    print(f"Total Frames: {analytics.total_frames}")
    print(f"Average Confidence: {analytics.average_confidence:.2%}")
    print(f"Motion Intensity: {analytics.motion_intensity:.4f}")
    print(f"Smoothness Score: {analytics.smoothness_score:.4f}")
    print(f"Tracking Quality: {analytics.tracking_quality:.2%}")
    print(f"Key Poses: {len(analytics.key_poses)}")
    
    # Create report
    processor.create_motion_report()
    
    # Export to BVH
    processor.export_to_bvh("motion_data.bvh")

if __name__ == "__main__":
    main()
