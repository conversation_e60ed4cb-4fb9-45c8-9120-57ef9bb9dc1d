# Getting started with an existing Unity version control repository

Suppose you want to start working on a Unity project in an existing Unity version control repository and already have a Unity version control account linked to your Unity ID. In that case, you will be able to open the project straight from the **Unity Hub**. A workspace will automatically be created for your project on your machine.

1. In the Unity Hub v3 Beta, click **Open** > **Open remote project** to see the list of your Unity version control repositories that contain a Unity project.
2. Click the project and click **Next**.
3. Click the Editor version and platform and click the **change version** button.
4. In the Editor pop-up, click the **Migrate** button to migrate your local workspace to a Unity version control workspace 
5. Once the migration is completed, click the **Open Unity version control** button.

![Plastic Hub](images/plasticHub.gif)

## Accessing the Unity version control Window

You can access the **Unity version control** window in the Unity Editor by clicking **Window** &gt; **Unity version control**.
