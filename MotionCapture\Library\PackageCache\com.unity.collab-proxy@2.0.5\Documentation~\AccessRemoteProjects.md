# Access remote projects 

1. In the Unity Hub v3, click **Open** > **Open Remote Project** to see the list of your version control repositories that contain a Unity project.
2. Select the project and click **Next**.
3. Select the Editor version and platform and click the **change version** button.
4. Your local version control workspace will be created for you. The latest version of the project will be downloaded and the Editor will open with the latest version of your Unity project. 