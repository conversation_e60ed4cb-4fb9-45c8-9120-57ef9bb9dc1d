"""
Configuration Management System
Handles loading, saving, and validation of system configurations
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict, field
import logging

logger = logging.getLogger(__name__)

@dataclass
class CameraConfig:
    """Camera configuration settings"""
    camera_id: int = 0
    width: int = 640
    height: int = 480
    fps: int = 30
    auto_exposure: bool = True
    exposure: int = -6
    brightness: int = 0
    contrast: int = 0
    saturation: int = 0
    hue: int = 0

@dataclass
class DetectionConfig:
    """Pose detection configuration"""
    model_complexity: int = 1
    min_detection_confidence: float = 0.5
    min_tracking_confidence: float = 0.5
    enable_segmentation: bool = False
    max_num_poses: int = 2
    static_image_mode: bool = False

@dataclass
class ProcessingConfig:
    """Data processing configuration"""
    enable_smoothing: bool = True
    smoothing_factor: float = 0.7
    kalman_process_noise: float = 1e-3
    kalman_measurement_noise: float = 1e-1
    enable_filtering: bool = True
    confidence_threshold: float = 0.3
    outlier_detection: bool = True
    outlier_threshold: float = 3.0

@dataclass
class TrackingConfig:
    """Multi-person tracking configuration"""
    max_tracking_distance: float = 100.0
    max_frames_without_detection: int = 30
    enable_id_consistency: bool = True
    track_history_length: int = 30

@dataclass
class OutputConfig:
    """Output configuration"""
    output_format: str = "both"  # json, txt, both
    output_directory: str = "output"
    filename_prefix: str = "motion_data"
    include_timestamp: bool = True
    save_video: bool = False
    video_codec: str = "mp4v"
    video_quality: int = 90

@dataclass
class RealTimeConfig:
    """Real-time communication configuration"""
    enable_realtime: bool = False
    protocol: str = "tcp"  # tcp, udp, websocket
    host: str = "localhost"
    port: int = 12345
    buffer_size: int = 1024
    timeout: float = 5.0
    compression: bool = False

@dataclass
class VisualizationConfig:
    """Visualization configuration"""
    show_preview: bool = True
    preview_width: int = 640
    preview_height: int = 480
    show_landmarks: bool = True
    show_connections: bool = True
    show_bounding_box: bool = True
    show_confidence: bool = True
    landmark_size: int = 3
    line_thickness: int = 2
    colors: Dict[str, List[int]] = field(default_factory=lambda: {
        "left_side": [255, 0, 0],
        "right_side": [0, 0, 255],
        "center": [0, 255, 0],
        "bbox": [255, 255, 0]
    })

@dataclass
class UnityConfig:
    """Unity-specific configuration"""
    joint_scale: float = 0.01
    bone_width: float = 0.02
    enable_smoothing: bool = True
    smoothing_factor: float = 0.1
    enable_glow: bool = False
    enable_trails: bool = False
    enable_particles: bool = False
    animation_speed: float = 30.0
    loop_animation: bool = True

@dataclass
class PerformanceConfig:
    """Performance optimization configuration"""
    max_fps: int = 30
    skip_frames: int = 0
    use_threading: bool = True
    thread_count: int = 2
    gpu_acceleration: bool = False
    memory_limit_mb: int = 1024
    enable_profiling: bool = False

@dataclass
class SystemConfig:
    """Complete system configuration"""
    camera: CameraConfig = field(default_factory=CameraConfig)
    detection: DetectionConfig = field(default_factory=DetectionConfig)
    processing: ProcessingConfig = field(default_factory=ProcessingConfig)
    tracking: TrackingConfig = field(default_factory=TrackingConfig)
    output: OutputConfig = field(default_factory=OutputConfig)
    realtime: RealTimeConfig = field(default_factory=RealTimeConfig)
    visualization: VisualizationConfig = field(default_factory=VisualizationConfig)
    unity: UnityConfig = field(default_factory=UnityConfig)
    performance: PerformanceConfig = field(default_factory=PerformanceConfig)
    
    # Metadata
    version: str = "2.0.0"
    created_by: str = "Enhanced Motion Capture System"
    description: str = "Default configuration"

class ConfigManager:
    """Manages system configuration"""
    
    DEFAULT_CONFIG_DIR = Path("configs")
    DEFAULT_CONFIG_FILE = "default_config.json"
    USER_CONFIG_FILE = "user_config.json"
    
    def __init__(self, config_dir: Optional[Path] = None):
        self.config_dir = config_dir or self.DEFAULT_CONFIG_DIR
        self.config_dir.mkdir(exist_ok=True)
        
        self.current_config: Optional[SystemConfig] = None
        self.config_history: List[SystemConfig] = []
        
        # Load default configuration
        self.load_default_config()
    
    def load_default_config(self) -> SystemConfig:
        """Load default configuration"""
        default_path = self.config_dir / self.DEFAULT_CONFIG_FILE
        
        if default_path.exists():
            try:
                self.current_config = self.load_config(default_path)
                logger.info(f"Loaded default config from {default_path}")
            except Exception as e:
                logger.warning(f"Failed to load default config: {e}")
                self.current_config = SystemConfig()
        else:
            self.current_config = SystemConfig()
            self.save_config(default_path, self.current_config)
            logger.info(f"Created default config at {default_path}")
        
        return self.current_config
    
    def load_user_config(self) -> SystemConfig:
        """Load user configuration"""
        user_path = self.config_dir / self.USER_CONFIG_FILE
        
        if user_path.exists():
            try:
                self.current_config = self.load_config(user_path)
                logger.info(f"Loaded user config from {user_path}")
            except Exception as e:
                logger.error(f"Failed to load user config: {e}")
                # Fall back to default
                self.load_default_config()
        else:
            logger.info("No user config found, using default")
        
        return self.current_config
    
    def load_config(self, file_path: Path) -> SystemConfig:
        """Load configuration from file"""
        try:
            with open(file_path, 'r') as f:
                config_data = json.load(f)
            
            # Validate and create config object
            config = self._dict_to_config(config_data)
            
            # Add to history
            self.config_history.append(config)
            
            return config
            
        except Exception as e:
            logger.error(f"Error loading config from {file_path}: {e}")
            raise
    
    def save_config(self, file_path: Path, config: SystemConfig) -> bool:
        """Save configuration to file"""
        try:
            config_dict = asdict(config)
            
            with open(file_path, 'w') as f:
                json.dump(config_dict, f, indent=2)
            
            logger.info(f"Saved config to {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving config to {file_path}: {e}")
            return False
    
    def save_user_config(self) -> bool:
        """Save current configuration as user config"""
        if self.current_config is None:
            logger.error("No current config to save")
            return False
        
        user_path = self.config_dir / self.USER_CONFIG_FILE
        return self.save_config(user_path, self.current_config)
    
    def _dict_to_config(self, config_dict: Dict[str, Any]) -> SystemConfig:
        """Convert dictionary to SystemConfig object"""
        try:
            # Create sub-configs
            camera = CameraConfig(**config_dict.get('camera', {}))
            detection = DetectionConfig(**config_dict.get('detection', {}))
            processing = ProcessingConfig(**config_dict.get('processing', {}))
            tracking = TrackingConfig(**config_dict.get('tracking', {}))
            output = OutputConfig(**config_dict.get('output', {}))
            realtime = RealTimeConfig(**config_dict.get('realtime', {}))
            visualization = VisualizationConfig(**config_dict.get('visualization', {}))
            unity = UnityConfig(**config_dict.get('unity', {}))
            performance = PerformanceConfig(**config_dict.get('performance', {}))
            
            # Create main config
            config = SystemConfig(
                camera=camera,
                detection=detection,
                processing=processing,
                tracking=tracking,
                output=output,
                realtime=realtime,
                visualization=visualization,
                unity=unity,
                performance=performance,
                version=config_dict.get('version', '2.0.0'),
                created_by=config_dict.get('created_by', 'Enhanced Motion Capture System'),
                description=config_dict.get('description', 'Loaded configuration')
            )
            
            return config
            
        except Exception as e:
            logger.error(f"Error converting dict to config: {e}")
            raise
    
    def validate_config(self, config: SystemConfig) -> List[str]:
        """Validate configuration and return list of issues"""
        issues = []
        
        # Validate camera config
        if config.camera.width <= 0 or config.camera.height <= 0:
            issues.append("Invalid camera resolution")
        
        if config.camera.fps <= 0 or config.camera.fps > 120:
            issues.append("Invalid camera FPS")
        
        # Validate detection config
        if not (0.0 <= config.detection.min_detection_confidence <= 1.0):
            issues.append("Detection confidence must be between 0.0 and 1.0")
        
        if not (0.0 <= config.detection.min_tracking_confidence <= 1.0):
            issues.append("Tracking confidence must be between 0.0 and 1.0")
        
        # Validate processing config
        if not (0.0 <= config.processing.smoothing_factor <= 1.0):
            issues.append("Smoothing factor must be between 0.0 and 1.0")
        
        if not (0.0 <= config.processing.confidence_threshold <= 1.0):
            issues.append("Confidence threshold must be between 0.0 and 1.0")
        
        # Validate real-time config
        if config.realtime.port <= 0 or config.realtime.port > 65535:
            issues.append("Invalid port number")
        
        # Validate performance config
        if config.performance.max_fps <= 0:
            issues.append("Max FPS must be positive")
        
        if config.performance.memory_limit_mb <= 0:
            issues.append("Memory limit must be positive")
        
        return issues
    
    def get_config(self) -> SystemConfig:
        """Get current configuration"""
        return self.current_config
    
    def update_config(self, **kwargs) -> bool:
        """Update current configuration"""
        if self.current_config is None:
            logger.error("No current config to update")
            return False
        
        try:
            # Update configuration fields
            for key, value in kwargs.items():
                if hasattr(self.current_config, key):
                    setattr(self.current_config, key, value)
                else:
                    logger.warning(f"Unknown config field: {key}")
            
            # Validate updated config
            issues = self.validate_config(self.current_config)
            if issues:
                logger.warning(f"Config validation issues: {issues}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error updating config: {e}")
            return False
    
    def reset_to_default(self) -> SystemConfig:
        """Reset configuration to default"""
        self.current_config = SystemConfig()
        logger.info("Reset configuration to default")
        return self.current_config
    
    def list_saved_configs(self) -> List[str]:
        """List all saved configuration files"""
        config_files = []
        
        for file_path in self.config_dir.glob("*.json"):
            config_files.append(file_path.stem)
        
        return sorted(config_files)
    
    def export_config(self, export_path: Path, config: Optional[SystemConfig] = None) -> bool:
        """Export configuration to specified path"""
        config = config or self.current_config
        
        if config is None:
            logger.error("No config to export")
            return False
        
        return self.save_config(export_path, config)
    
    def import_config(self, import_path: Path) -> SystemConfig:
        """Import configuration from specified path"""
        return self.load_config(import_path)
    
    def get_config_summary(self) -> str:
        """Get human-readable configuration summary"""
        if self.current_config is None:
            return "No configuration loaded"
        
        config = self.current_config
        
        summary = f"""
Configuration Summary:
=====================
Version: {config.version}
Description: {config.description}

Camera:
  Resolution: {config.camera.width}x{config.camera.height}
  FPS: {config.camera.fps}
  Camera ID: {config.camera.camera_id}

Detection:
  Model Complexity: {config.detection.model_complexity}
  Detection Confidence: {config.detection.min_detection_confidence}
  Tracking Confidence: {config.detection.min_tracking_confidence}
  Max Poses: {config.detection.max_num_poses}

Processing:
  Smoothing: {config.processing.enable_smoothing}
  Smoothing Factor: {config.processing.smoothing_factor}
  Filtering: {config.processing.enable_filtering}
  Confidence Threshold: {config.processing.confidence_threshold}

Real-time:
  Enabled: {config.realtime.enable_realtime}
  Host: {config.realtime.host}
  Port: {config.realtime.port}

Output:
  Format: {config.output.output_format}
  Directory: {config.output.output_directory}
  Save Video: {config.output.save_video}

Performance:
  Max FPS: {config.performance.max_fps}
  Threading: {config.performance.use_threading}
  GPU Acceleration: {config.performance.gpu_acceleration}
"""
        
        return summary

# Global config manager instance
config_manager = ConfigManager()

def get_config() -> SystemConfig:
    """Get current system configuration"""
    return config_manager.get_config()

def save_user_config() -> bool:
    """Save current configuration as user config"""
    return config_manager.save_user_config()

def load_user_config() -> SystemConfig:
    """Load user configuration"""
    return config_manager.load_user_config()

# Example usage
if __name__ == "__main__":
    # Create config manager
    cm = ConfigManager()
    
    # Get current config
    config = cm.get_config()
    print(cm.get_config_summary())
    
    # Update some settings
    cm.update_config(
        camera=CameraConfig(width=1280, height=720, fps=60),
        detection=DetectionConfig(min_detection_confidence=0.7)
    )
    
    # Save user config
    cm.save_user_config()
    
    # Validate config
    issues = cm.validate_config(config)
    if issues:
        print(f"Configuration issues: {issues}")
    else:
        print("Configuration is valid")
