# About Visual Scripting

Use Visual Scripting to create logic for games or applications without hand-coded C# scripts. Visual Scripting uses visual, node-based graphs, which both programmers and non-programmers use to design final logic or create prototypes. Visual Scripting also has an API that programmers can use for more advanced tasks, or to create custom nodes for other team members.

Visual Scripting nodes can represent functions, operators, and variables. Connect these nodes from their ports with edges to design your logic visually.

## Installation

Starting from Unity 2021 LTS onward, Visual Scripting is installed as a package in all new projects. For more information on packages, see the [Packages section](https://docs.unity3d.com/Manual/PackagesList.html) in the Unity User Manual. For earlier versions of Unity, Visual Scripting was available on the Asset Store, but that option has been deprecated. 

## Configure Visual Scripting

> [!NOTE]
> To use Visual Scripting in a project for the first time, you must [initialize it](vs-configuration.md#Initialize) from the Editor's [Project Settings](https://docs.unity3d.com/Manual/comp-ManagerGroup.html) window. 

To get started with Visual Scripting, [configure your project settings](vs-configuration.md) and [configure your preferences](vs-set-preferences.md).

## Choose a control scheme

Learn about the common keyboard shortcuts and [choose the control scheme](vs-control-schemes.md) that suits your needs.

## Update Visual Scripting

Learn how to [update Visual Scripting](vs-update.md) and [create and restore backups](vs-create-restore-backups.md).

## System requirements

Visual Scripting has no external dependencies.



