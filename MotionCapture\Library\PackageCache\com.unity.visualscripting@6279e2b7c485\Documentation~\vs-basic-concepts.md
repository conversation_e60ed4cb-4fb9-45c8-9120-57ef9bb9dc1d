# Basic concepts in Visual Scripting

> [!NOTE]
> For versions 2019/2020 LTS, download the Visual Scripting package from the [Unity Asset Store](https://assetstore.unity.com/packages/tools/visual-bolt-163802).

In this section, you can find information about basic concepts in Visual Scripting. These concepts will help you create logic for your application. 

## The interface 

The Visual Scripting interface starts with the Graph window. For more information, see [The interface](vs-interface-overview.md).

## Nodes

Nodes are the most basic part of creating scripts in Visual Scripting. For more information, see [Nodes](vs-nodes.md).

## Graphs and Machines 

Graphs contain the visual representations of logic in your application. To use a graph, you attach it to a Script Machine or State Machine on a GameObject. For more information about graphs, see [Graphs](vs-graph-types.md). For more information about Script Machines and State Machines, see [Script Machines and State Machines](vs-graph-machine-types.md).

## Variables 

Variables act as a container for a piece of information that might change as your application runs. For more information, see [Variables](vs-variables.md).

## Object types 

Variables, data, and objects in Visual Scripting all have a specific type. For more information, see [Object types](vs-types.md).





