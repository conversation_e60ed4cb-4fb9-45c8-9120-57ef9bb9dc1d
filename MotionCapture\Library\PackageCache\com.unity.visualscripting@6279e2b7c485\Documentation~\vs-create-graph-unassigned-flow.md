# Create a new unassigned graph with the empty graph creation flow

You can use the empty graph creation flow to create a new unassigned graph for use in your project. 

![The Empty Graph Creation Flow window](images/vs-empty-graph-create-flow.png)

> [!NOTE]
> To use the graph file, you must [attach it to a Script Machine or State Machine](vs-attach-graph-machine.md).

For more information on other ways to create a graph file, see [Create a new graph file](vs-create-graph.md).

To create a new unassigned graph: 

1. [!include[vs-visual-scripting-window](./snippets/vs-visual-scripting-window.md)]

2. In the new Visual Scripting window, select one of the following options: 

    * To create a new Script Graph, select **Create new Script Graph**. 
    * To create a new State Graph, select **Create new State Graph**. 

3. Choose a location to save the new graph file.

1. Enter a name for the graph.

1. Select **Save**.

    The new graph file automatically opens in a new window. 

The new graph file should look similar to the following image: 

![A new Script Graph, created with the empty graph creation flow with starter nodes](images/vs-new-graph-starter-nodes.png)

## Next steps 

After you create a new graph, attach it to a Script Machine or State Machine to use it in your application. For more information, see [Attach a graph file to a Script Machine or State Machine](vs-attach-graph-machine.md).
