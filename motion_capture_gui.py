"""
GUI Application for Motion Capture System
Provides easy-to-use interface for motion capture operations
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import queue
import json
from pathlib import Path
import subprocess
import sys
import os

try:
    from MotionCap import MotionCaptureSystem, MotionCaptureConfig
    from data_processor import DataProcessor
except ImportError:
    print("Required modules not found. Make sure MotionCap.py and data_processor.py are in the same directory.")
    sys.exit(1)

class MotionCaptureGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Enhanced 3D Motion Capture System")
        self.root.geometry("800x600")
        
        # Motion capture system
        self.motion_capture = None
        self.capture_thread = None
        self.is_capturing = False
        
        # Status queue for thread communication
        self.status_queue = queue.Queue()
        
        self.setup_ui()
        self.load_default_config()
        
        # Start status update loop
        self.root.after(100, self.update_status)
    
    def setup_ui(self):
        """Setup the user interface"""
        # Create notebook for tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Capture tab
        self.capture_frame = ttk.Frame(notebook)
        notebook.add(self.capture_frame, text="Capture")
        self.setup_capture_tab()
        
        # Settings tab
        self.settings_frame = ttk.Frame(notebook)
        notebook.add(self.settings_frame, text="Settings")
        self.setup_settings_tab()
        
        # Analysis tab
        self.analysis_frame = ttk.Frame(notebook)
        notebook.add(self.analysis_frame, text="Analysis")
        self.setup_analysis_tab()
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def setup_capture_tab(self):
        """Setup the capture control tab"""
        # Input source selection
        input_frame = ttk.LabelFrame(self.capture_frame, text="Input Source")
        input_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.input_var = tk.StringVar(value="webcam")
        ttk.Radiobutton(input_frame, text="Webcam", variable=self.input_var, value="webcam").pack(side=tk.LEFT)
        ttk.Radiobutton(input_frame, text="Video File", variable=self.input_var, value="video").pack(side=tk.LEFT)
        ttk.Radiobutton(input_frame, text="IP Camera", variable=self.input_var, value="ip_camera").pack(side=tk.LEFT)
        
        # File selection
        file_frame = ttk.Frame(self.capture_frame)
        file_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(file_frame, text="Video File:").pack(side=tk.LEFT)
        self.video_path_var = tk.StringVar(value="Video.mp4")
        ttk.Entry(file_frame, textvariable=self.video_path_var, width=40).pack(side=tk.LEFT, padx=5)
        ttk.Button(file_frame, text="Browse", command=self.browse_video_file).pack(side=tk.LEFT)
        
        # Camera settings
        camera_frame = ttk.LabelFrame(self.capture_frame, text="Camera Settings")
        camera_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(camera_frame, text="Camera ID:").grid(row=0, column=0, sticky=tk.W)
        self.camera_id_var = tk.IntVar(value=0)
        ttk.Spinbox(camera_frame, from_=0, to=10, textvariable=self.camera_id_var, width=10).grid(row=0, column=1, sticky=tk.W)
        
        ttk.Label(camera_frame, text="Resolution:").grid(row=0, column=2, sticky=tk.W, padx=(20,0))
        self.width_var = tk.IntVar(value=640)
        self.height_var = tk.IntVar(value=480)
        res_frame = ttk.Frame(camera_frame)
        res_frame.grid(row=0, column=3, sticky=tk.W)
        ttk.Entry(res_frame, textvariable=self.width_var, width=8).pack(side=tk.LEFT)
        ttk.Label(res_frame, text="x").pack(side=tk.LEFT)
        ttk.Entry(res_frame, textvariable=self.height_var, width=8).pack(side=tk.LEFT)
        
        # Control buttons
        control_frame = ttk.Frame(self.capture_frame)
        control_frame.pack(fill=tk.X, padx=5, pady=20)
        
        self.start_button = ttk.Button(control_frame, text="Start Capture", command=self.start_capture)
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(control_frame, text="Stop Capture", command=self.stop_capture, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        self.save_button = ttk.Button(control_frame, text="Save Data", command=self.save_data)
        self.save_button.pack(side=tk.LEFT, padx=5)
        
        # Real-time options
        realtime_frame = ttk.LabelFrame(self.capture_frame, text="Real-time Options")
        realtime_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.realtime_var = tk.BooleanVar()
        ttk.Checkbutton(realtime_frame, text="Enable Unity Connection", variable=self.realtime_var).pack(side=tk.LEFT)
        
        ttk.Label(realtime_frame, text="Host:").pack(side=tk.LEFT, padx=(20,0))
        self.unity_host_var = tk.StringVar(value="localhost")
        ttk.Entry(realtime_frame, textvariable=self.unity_host_var, width=15).pack(side=tk.LEFT, padx=5)
        
        ttk.Label(realtime_frame, text="Port:").pack(side=tk.LEFT)
        self.unity_port_var = tk.IntVar(value=12345)
        ttk.Entry(realtime_frame, textvariable=self.unity_port_var, width=8).pack(side=tk.LEFT, padx=5)
        
        # Status display
        status_frame = ttk.LabelFrame(self.capture_frame, text="Capture Status")
        status_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.status_text = tk.Text(status_frame, height=10, state=tk.DISABLED)
        scrollbar = ttk.Scrollbar(status_frame, orient=tk.VERTICAL, command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=scrollbar.set)
        
        self.status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def setup_settings_tab(self):
        """Setup the settings tab"""
        # Detection settings
        detection_frame = ttk.LabelFrame(self.settings_frame, text="Detection Settings")
        detection_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(detection_frame, text="Detection Confidence:").grid(row=0, column=0, sticky=tk.W)
        self.detection_conf_var = tk.DoubleVar(value=0.5)
        ttk.Scale(detection_frame, from_=0.0, to=1.0, variable=self.detection_conf_var, orient=tk.HORIZONTAL).grid(row=0, column=1, sticky=tk.EW)
        ttk.Label(detection_frame, textvariable=self.detection_conf_var).grid(row=0, column=2)
        
        ttk.Label(detection_frame, text="Tracking Confidence:").grid(row=1, column=0, sticky=tk.W)
        self.tracking_conf_var = tk.DoubleVar(value=0.5)
        ttk.Scale(detection_frame, from_=0.0, to=1.0, variable=self.tracking_conf_var, orient=tk.HORIZONTAL).grid(row=1, column=1, sticky=tk.EW)
        ttk.Label(detection_frame, textvariable=self.tracking_conf_var).grid(row=1, column=2)
        
        detection_frame.columnconfigure(1, weight=1)
        
        # Processing settings
        processing_frame = ttk.LabelFrame(self.settings_frame, text="Processing Settings")
        processing_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.smoothing_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(processing_frame, text="Enable Smoothing", variable=self.smoothing_var).grid(row=0, column=0, sticky=tk.W)
        
        ttk.Label(processing_frame, text="Smoothing Factor:").grid(row=0, column=1, sticky=tk.W, padx=(20,0))
        self.smoothing_factor_var = tk.DoubleVar(value=0.7)
        ttk.Scale(processing_frame, from_=0.0, to=1.0, variable=self.smoothing_factor_var, orient=tk.HORIZONTAL, length=200).grid(row=0, column=2)
        
        self.filtering_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(processing_frame, text="Enable Filtering", variable=self.filtering_var).grid(row=1, column=0, sticky=tk.W)
        
        ttk.Label(processing_frame, text="Confidence Threshold:").grid(row=1, column=1, sticky=tk.W, padx=(20,0))
        self.conf_threshold_var = tk.DoubleVar(value=0.3)
        ttk.Scale(processing_frame, from_=0.0, to=1.0, variable=self.conf_threshold_var, orient=tk.HORIZONTAL, length=200).grid(row=1, column=2)
        
        # Output settings
        output_frame = ttk.LabelFrame(self.settings_frame, text="Output Settings")
        output_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(output_frame, text="Output Format:").grid(row=0, column=0, sticky=tk.W)
        self.output_format_var = tk.StringVar(value="both")
        format_combo = ttk.Combobox(output_frame, textvariable=self.output_format_var, values=["json", "txt", "both"], state="readonly")
        format_combo.grid(row=0, column=1, sticky=tk.W)
        
        self.save_video_var = tk.BooleanVar()
        ttk.Checkbutton(output_frame, text="Save Processed Video", variable=self.save_video_var).grid(row=1, column=0, columnspan=2, sticky=tk.W)
        
        # Config buttons
        config_frame = ttk.Frame(self.settings_frame)
        config_frame.pack(fill=tk.X, padx=5, pady=20)
        
        ttk.Button(config_frame, text="Save Config", command=self.save_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(config_frame, text="Load Config", command=self.load_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(config_frame, text="Reset to Defaults", command=self.reset_config).pack(side=tk.LEFT, padx=5)
    
    def setup_analysis_tab(self):
        """Setup the analysis tab"""
        # File selection
        file_frame = ttk.Frame(self.analysis_frame)
        file_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(file_frame, text="Data File:").pack(side=tk.LEFT)
        self.analysis_file_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.analysis_file_var, width=50).pack(side=tk.LEFT, padx=5)
        ttk.Button(file_frame, text="Browse", command=self.browse_analysis_file).pack(side=tk.LEFT)
        
        # Analysis buttons
        button_frame = ttk.Frame(self.analysis_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=10)
        
        ttk.Button(button_frame, text="Analyze Motion", command=self.analyze_motion).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Generate Report", command=self.generate_report).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Export BVH", command=self.export_bvh).pack(side=tk.LEFT, padx=5)
        
        # Results display
        results_frame = ttk.LabelFrame(self.analysis_frame, text="Analysis Results")
        results_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.results_text = tk.Text(results_frame, state=tk.DISABLED)
        results_scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=results_scrollbar.set)
        
        self.results_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        results_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def browse_video_file(self):
        """Browse for video file"""
        filename = filedialog.askopenfilename(
            title="Select Video File",
            filetypes=[("Video files", "*.mp4 *.avi *.mov *.mkv"), ("All files", "*.*")]
        )
        if filename:
            self.video_path_var.set(filename)
    
    def browse_analysis_file(self):
        """Browse for analysis file"""
        filename = filedialog.askopenfilename(
            title="Select Motion Data File",
            filetypes=[("JSON files", "*.json"), ("Text files", "*.txt"), ("All files", "*.*")]
        )
        if filename:
            self.analysis_file_var.set(filename)
    
    def create_config(self):
        """Create configuration from GUI settings"""
        return MotionCaptureConfig(
            input_source=self.input_var.get(),
            video_path=self.video_path_var.get(),
            camera_id=self.camera_id_var.get(),
            detection_confidence=self.detection_conf_var.get(),
            tracking_confidence=self.tracking_conf_var.get(),
            enable_smoothing=self.smoothing_var.get(),
            smoothing_factor=self.smoothing_factor_var.get(),
            enable_filtering=self.filtering_var.get(),
            confidence_threshold=self.conf_threshold_var.get(),
            output_format=self.output_format_var.get(),
            save_video=self.save_video_var.get(),
            enable_realtime=self.realtime_var.get(),
            unity_host=self.unity_host_var.get(),
            unity_port=self.unity_port_var.get(),
            preview_width=self.width_var.get(),
            preview_height=self.height_var.get()
        )
    
    def start_capture(self):
        """Start motion capture"""
        if self.is_capturing:
            return
        
        try:
            config = self.create_config()
            self.motion_capture = MotionCaptureSystem(config)
            
            # Start capture in separate thread
            self.capture_thread = threading.Thread(target=self.run_capture)
            self.capture_thread.daemon = True
            self.capture_thread.start()
            
            self.is_capturing = True
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
            
            self.log_status("Motion capture started")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to start capture: {e}")
    
    def run_capture(self):
        """Run motion capture in separate thread"""
        try:
            self.motion_capture.run()
        except Exception as e:
            self.status_queue.put(f"Capture error: {e}")
        finally:
            self.status_queue.put("CAPTURE_STOPPED")
    
    def stop_capture(self):
        """Stop motion capture"""
        if self.motion_capture:
            self.motion_capture.running = False
        
        self.is_capturing = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        
        self.log_status("Motion capture stopped")
    
    def save_data(self):
        """Save captured data"""
        if self.motion_capture and self.motion_capture.pose_frames:
            self.motion_capture.save_data()
            self.log_status("Data saved successfully")
        else:
            messagebox.showwarning("Warning", "No data to save")
    
    def analyze_motion(self):
        """Analyze motion data"""
        file_path = self.analysis_file_var.get()
        if not file_path or not Path(file_path).exists():
            messagebox.showerror("Error", "Please select a valid data file")
            return
        
        try:
            processor = DataProcessor()
            
            if file_path.endswith('.json'):
                processor.load_json_data(file_path)
            else:
                processor.load_txt_data(file_path)
            
            analytics = processor.generate_analytics()
            
            # Display results
            results = f"""Motion Analysis Results:
            
Total Frames: {analytics.total_frames}
Average Confidence: {analytics.average_confidence:.2%}
Motion Intensity: {analytics.motion_intensity:.4f}
Smoothness Score: {analytics.smoothness_score:.4f}
Tracking Quality: {analytics.tracking_quality:.2%}
Key Poses Detected: {len(analytics.key_poses)}
Key Pose Frames: {', '.join(map(str, analytics.key_poses[:20]))}
"""
            
            self.results_text.config(state=tk.NORMAL)
            self.results_text.delete(1.0, tk.END)
            self.results_text.insert(tk.END, results)
            self.results_text.config(state=tk.DISABLED)
            
        except Exception as e:
            messagebox.showerror("Error", f"Analysis failed: {e}")
    
    def generate_report(self):
        """Generate HTML report"""
        file_path = self.analysis_file_var.get()
        if not file_path or not Path(file_path).exists():
            messagebox.showerror("Error", "Please select a valid data file")
            return
        
        try:
            processor = DataProcessor()
            
            if file_path.endswith('.json'):
                processor.load_json_data(file_path)
            else:
                processor.load_txt_data(file_path)
            
            processor.create_motion_report()
            messagebox.showinfo("Success", "Report generated as motion_report.html")
            
        except Exception as e:
            messagebox.showerror("Error", f"Report generation failed: {e}")
    
    def export_bvh(self):
        """Export to BVH format"""
        file_path = self.analysis_file_var.get()
        if not file_path or not Path(file_path).exists():
            messagebox.showerror("Error", "Please select a valid data file")
            return
        
        output_path = filedialog.asksaveasfilename(
            title="Save BVH File",
            defaultextension=".bvh",
            filetypes=[("BVH files", "*.bvh"), ("All files", "*.*")]
        )
        
        if not output_path:
            return
        
        try:
            processor = DataProcessor()
            
            if file_path.endswith('.json'):
                processor.load_json_data(file_path)
            else:
                processor.load_txt_data(file_path)
            
            processor.export_to_bvh(output_path)
            messagebox.showinfo("Success", f"BVH exported to {output_path}")
            
        except Exception as e:
            messagebox.showerror("Error", f"BVH export failed: {e}")
    
    def save_config(self):
        """Save current configuration"""
        filename = filedialog.asksaveasfilename(
            title="Save Configuration",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                config = self.create_config()
                with open(filename, 'w') as f:
                    json.dump(config.__dict__, f, indent=2)
                messagebox.showinfo("Success", "Configuration saved")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save config: {e}")
    
    def load_config(self):
        """Load configuration from file"""
        filename = filedialog.askopenfilename(
            title="Load Configuration",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'r') as f:
                    config_data = json.load(f)
                
                # Update GUI with loaded values
                self.input_var.set(config_data.get('input_source', 'webcam'))
                self.video_path_var.set(config_data.get('video_path', 'Video.mp4'))
                self.camera_id_var.set(config_data.get('camera_id', 0))
                self.detection_conf_var.set(config_data.get('detection_confidence', 0.5))
                self.tracking_conf_var.set(config_data.get('tracking_confidence', 0.5))
                self.smoothing_var.set(config_data.get('enable_smoothing', True))
                self.smoothing_factor_var.set(config_data.get('smoothing_factor', 0.7))
                self.filtering_var.set(config_data.get('enable_filtering', True))
                self.conf_threshold_var.set(config_data.get('confidence_threshold', 0.3))
                self.output_format_var.set(config_data.get('output_format', 'both'))
                self.save_video_var.set(config_data.get('save_video', False))
                self.realtime_var.set(config_data.get('enable_realtime', False))
                self.unity_host_var.set(config_data.get('unity_host', 'localhost'))
                self.unity_port_var.set(config_data.get('unity_port', 12345))
                self.width_var.set(config_data.get('preview_width', 640))
                self.height_var.set(config_data.get('preview_height', 480))
                
                messagebox.showinfo("Success", "Configuration loaded")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load config: {e}")
    
    def load_default_config(self):
        """Load default configuration"""
        # Default values are already set in variable initialization
        pass
    
    def reset_config(self):
        """Reset to default configuration"""
        self.input_var.set("webcam")
        self.video_path_var.set("Video.mp4")
        self.camera_id_var.set(0)
        self.detection_conf_var.set(0.5)
        self.tracking_conf_var.set(0.5)
        self.smoothing_var.set(True)
        self.smoothing_factor_var.set(0.7)
        self.filtering_var.set(True)
        self.conf_threshold_var.set(0.3)
        self.output_format_var.set("both")
        self.save_video_var.set(False)
        self.realtime_var.set(False)
        self.unity_host_var.set("localhost")
        self.unity_port_var.set(12345)
        self.width_var.set(640)
        self.height_var.set(480)
        
        messagebox.showinfo("Success", "Configuration reset to defaults")
    
    def log_status(self, message):
        """Log status message"""
        self.status_text.config(state=tk.NORMAL)
        self.status_text.insert(tk.END, f"{message}\n")
        self.status_text.see(tk.END)
        self.status_text.config(state=tk.DISABLED)
    
    def update_status(self):
        """Update status from queue"""
        try:
            while True:
                message = self.status_queue.get_nowait()
                if message == "CAPTURE_STOPPED":
                    self.stop_capture()
                else:
                    self.log_status(message)
        except queue.Empty:
            pass
        
        # Schedule next update
        self.root.after(100, self.update_status)

def main():
    root = tk.Tk()
    app = MotionCaptureGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
