#!/usr/bin/env python3
"""
Comprehensive System Test
Tests all components of the Enhanced Motion Capture System
"""

import sys
import time
import numpy as np
from pathlib import Path
import json

def test_imports():
    """Test all module imports"""
    print("Testing imports...")
    
    try:
        import cv2
        print(f"✅ OpenCV {cv2.__version__}")
    except ImportError:
        print("❌ OpenCV not available")
        return False
    
    try:
        import mediapipe as mp
        print(f"✅ MediaPipe available")
    except ImportError:
        print("❌ MediaPipe not available")
        return False
    
    try:
        from MotionCap import MotionCaptureSystem, MotionCaptureConfig
        print("✅ Enhanced MotionCap module")
    except ImportError as e:
        print(f"❌ MotionCap module: {e}")
        return False
    
    try:
        from advanced_pose_detector import AdvancedPoseDetector
        print("✅ Advanced pose detector")
    except ImportError as e:
        print(f"❌ Advanced pose detector: {e}")
        return False
    
    try:
        from holistic_detector import HolisticDetector
        print("✅ Holistic detector")
    except ImportError as e:
        print(f"❌ Holistic detector: {e}")
        return False
    
    try:
        from multi_camera_system import CameraManager, MultiCameraPoseEstimator
        print("✅ Multi-camera system")
    except ImportError as e:
        print(f"❌ Multi-camera system: {e}")
        return False
    
    try:
        from data_processor import DataProcessor
        print("✅ Data processor")
    except ImportError as e:
        print(f"❌ Data processor: {e}")
        return False
    
    try:
        from config_manager import ConfigManager
        print("✅ Configuration manager")
    except ImportError as e:
        print(f"❌ Configuration manager: {e}")
        return False
    
    try:
        from export_system import BVHExporter, MotionLibrary
        print("✅ Export system")
    except ImportError as e:
        print(f"❌ Export system: {e}")
        return False
    
    return True

def test_basic_pose_detection():
    """Test basic pose detection functionality"""
    print("\nTesting basic pose detection...")
    
    try:
        from advanced_pose_detector import AdvancedPoseDetector
        
        detector = AdvancedPoseDetector()
        
        # Create test image
        test_image = np.zeros((480, 640, 3), dtype=np.uint8)
        
        # Test detection (should return empty result for blank image)
        poses = detector.detect_poses(test_image)
        
        print(f"✅ Pose detection working (detected {len(poses)} poses)")
        
        detector.close()
        return True
        
    except Exception as e:
        print(f"❌ Pose detection failed: {e}")
        return False

def test_holistic_detection():
    """Test holistic detection (pose + hands + face)"""
    print("\nTesting holistic detection...")
    
    try:
        from holistic_detector import HolisticDetector
        
        detector = HolisticDetector()
        
        # Create test image
        test_image = np.zeros((480, 640, 3), dtype=np.uint8)
        
        # Test detection
        detection = detector.detect_holistic(test_image)
        
        print(f"✅ Holistic detection working")
        print(f"   Pose confidence: {detection.pose_confidence:.2f}")
        print(f"   Face confidence: {detection.face_confidence:.2f}")
        
        detector.close()
        return True
        
    except Exception as e:
        print(f"❌ Holistic detection failed: {e}")
        return False

def test_configuration_system():
    """Test configuration management"""
    print("\nTesting configuration system...")
    
    try:
        from config_manager import ConfigManager, SystemConfig
        
        config_manager = ConfigManager()
        config = config_manager.get_config()
        
        # Test configuration validation
        issues = config_manager.validate_config(config)
        
        if issues:
            print(f"⚠️  Configuration issues: {issues}")
        else:
            print("✅ Configuration system working")
        
        # Test saving/loading
        test_config_path = Path("test_config.json")
        config_manager.save_config(test_config_path, config)
        
        if test_config_path.exists():
            loaded_config = config_manager.load_config(test_config_path)
            print("✅ Configuration save/load working")
            test_config_path.unlink()  # Clean up
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration system failed: {e}")
        return False

def test_data_processing():
    """Test data processing and analysis"""
    print("\nTesting data processing...")
    
    try:
        from data_processor import DataProcessor
        
        processor = DataProcessor()
        
        # Create sample data
        sample_data = []
        for i in range(10):
            frame_data = {
                'frame_id': i,
                'timestamp': i / 30.0,
                'landmarks': [[j*10 + i, j*5, j*2, 0.9] for j in range(33)],
                'confidence': 0.9
            }
            sample_data.append(frame_data)
        
        processor.pose_data = sample_data
        
        # Test analytics
        analytics = processor.generate_analytics()
        
        print(f"✅ Data processing working")
        print(f"   Total frames: {analytics.total_frames}")
        print(f"   Motion intensity: {analytics.motion_intensity:.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Data processing failed: {e}")
        return False

def test_export_system():
    """Test export functionality"""
    print("\nTesting export system...")
    
    try:
        from export_system import BVHExporter, MotionLibrary, MotionFrame
        
        # Create sample motion data
        motion_frames = []
        for i in range(10):
            frame = MotionFrame(
                timestamp=i / 30.0,
                pose_landmarks=[[j*10 + i, j*5, j*2, 0.9] for j in range(33)]
            )
            motion_frames.append(frame)
        
        # Test BVH export
        bvh_exporter = BVHExporter()
        test_bvh_path = "test_export.bvh"
        
        if bvh_exporter.export_bvh(motion_frames, test_bvh_path):
            print("✅ BVH export working")
            Path(test_bvh_path).unlink()  # Clean up
        
        # Test motion library
        library = MotionLibrary("test_library")
        capture_id = library.add_capture("Test Capture", motion_frames)
        
        captures = library.list_captures()
        if len(captures) > 0:
            print("✅ Motion library working")
        
        # Clean up
        import shutil
        shutil.rmtree("test_library", ignore_errors=True)
        
        return True
        
    except Exception as e:
        print(f"❌ Export system failed: {e}")
        return False

def test_camera_access():
    """Test camera access"""
    print("\nTesting camera access...")
    
    try:
        import cv2
        
        cap = cv2.VideoCapture(0)
        
        if cap.isOpened():
            ret, frame = cap.read()
            if ret:
                print(f"✅ Camera access working (frame size: {frame.shape})")
                cap.release()
                return True
            else:
                print("⚠️  Camera opened but no frame received")
                cap.release()
                return False
        else:
            print("⚠️  No camera available (this is OK if no camera is connected)")
            return True
        
    except Exception as e:
        print(f"❌ Camera access failed: {e}")
        return False

def test_unity_compatibility():
    """Test Unity project compatibility"""
    print("\nTesting Unity compatibility...")
    
    unity_scripts = [
        "MotionCapture/Assets/AnimationCode.cs",
        "MotionCapture/Assets/LineCode.cs",
        "MotionCapture/Assets/RealTimeReceiver.cs",
        "MotionCapture/Assets/SkeletonManager.cs",
        "MotionCapture/Assets/IKSystem.cs",
        "MotionCapture/Assets/VisualizationController.cs"
    ]
    
    missing_scripts = []
    for script_path in unity_scripts:
        if not Path(script_path).exists():
            missing_scripts.append(script_path)
    
    if missing_scripts:
        print("❌ Missing Unity scripts:")
        for script in missing_scripts:
            print(f"   - {script}")
        return False
    else:
        print("✅ All Unity scripts present")
        return True

def test_gui_availability():
    """Test GUI availability"""
    print("\nTesting GUI availability...")
    
    try:
        import tkinter as tk
        
        # Test basic tkinter functionality
        root = tk.Tk()
        root.withdraw()  # Hide window
        root.destroy()
        
        print("✅ GUI (tkinter) available")
        
        # Check if GUI script exists
        if Path("motion_capture_gui.py").exists():
            print("✅ GUI application script available")
        else:
            print("❌ GUI application script missing")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ GUI not available: {e}")
        return False

def run_performance_test():
    """Run basic performance test"""
    print("\nRunning performance test...")
    
    try:
        from advanced_pose_detector import AdvancedPoseDetector
        
        detector = AdvancedPoseDetector()
        
        # Create test image
        test_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        
        # Time detection
        start_time = time.time()
        num_iterations = 10
        
        for _ in range(num_iterations):
            poses = detector.detect_poses(test_image)
        
        end_time = time.time()
        avg_time = (end_time - start_time) / num_iterations
        fps = 1.0 / avg_time
        
        print(f"✅ Performance test completed")
        print(f"   Average detection time: {avg_time*1000:.1f}ms")
        print(f"   Estimated FPS: {fps:.1f}")
        
        detector.close()
        
        if fps >= 15:
            print("✅ Performance is good for real-time use")
        elif fps >= 5:
            print("⚠️  Performance is acceptable but may struggle with real-time")
        else:
            print("❌ Performance is too slow for real-time use")
        
        return fps >= 5
        
    except Exception as e:
        print(f"❌ Performance test failed: {e}")
        return False

def main():
    """Run comprehensive system test"""
    print("Enhanced 3D Motion Capture System - Comprehensive Test")
    print("=" * 60)
    
    tests = [
        ("Module Imports", test_imports),
        ("Basic Pose Detection", test_basic_pose_detection),
        ("Holistic Detection", test_holistic_detection),
        ("Configuration System", test_configuration_system),
        ("Data Processing", test_data_processing),
        ("Export System", test_export_system),
        ("Camera Access", test_camera_access),
        ("Unity Compatibility", test_unity_compatibility),
        ("GUI Availability", test_gui_availability),
        ("Performance", run_performance_test)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} failed")
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! System is ready for use.")
        return 0
    elif passed >= total * 0.8:
        print("⚠️  Most tests passed. System should work with minor issues.")
        return 0
    else:
        print("❌ Many tests failed. Please check your installation.")
        return 1

if __name__ == "__main__":
    exit(main())
