using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;

public class VisualizationController : MonoBehaviour
{
    [Header("Visualization Settings")]
    public SkeletonManager skeletonManager;
    public AnimationCode animationCode;
    public RealTimeReceiver realTimeReceiver;
    
    [Header("Visual Effects")]
    public bool enableGlow = true;
    public bool enableTrails = false;
    public bool enableParticles = false;
    public bool enableDepthVisualization = false;
    
    [Header("Lighting")]
    public Light mainLight;
    public Color ambientColor = Color.gray;
    public float lightIntensity = 1.0f;
    public bool dynamicLighting = false;
    
    [Header("Materials")]
    public Material jointMaterial;
    public Material boneMaterial;
    public Material glowMaterial;
    public Material trailMaterial;
    
    [Header("Animation")]
    public bool enableAnimationBlending = true;
    public float blendSpeed = 5f;
    public AnimationCurve blendCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
    
    [Header("Post Processing")]
    public Volume postProcessVolume;
    public bool enableBloom = true;
    public bool enableColorGrading = true;
    public bool enableVignette = false;
    
    private TrailRenderer[] jointTrails;
    private ParticleSystem[] jointParticles;
    private Light[] jointLights;
    private Material[] originalJointMaterials;
    private Material[] originalBoneMaterials;
    
    // Animation blending
    private Vector3[] previousPositions;
    private Vector3[] currentPositions;
    private Vector3[] targetPositions;
    private float blendProgress = 0f;
    
    // Performance monitoring
    private int frameCount = 0;
    private float lastFPSUpdate = 0f;
    private float currentFPS = 0f;
    
    void Start()
    {
        InitializeVisualization();
        SetupPostProcessing();
        SetupLighting();
    }
    
    void InitializeVisualization()
    {
        if (skeletonManager == null)
        {
            skeletonManager = FindObjectOfType<SkeletonManager>();
        }
        
        if (animationCode == null)
        {
            animationCode = FindObjectOfType<AnimationCode>();
        }
        
        if (realTimeReceiver == null)
        {
            realTimeReceiver = FindObjectOfType<RealTimeReceiver>();
        }
        
        // Wait for skeleton to be created
        StartCoroutine(InitializeAfterSkeleton());
    }
    
    IEnumerator InitializeAfterSkeleton()
    {
        // Wait for skeleton manager to create joints
        while (skeletonManager == null || skeletonManager.GetJoints() == null)
        {
            yield return new WaitForSeconds(0.1f);
        }
        
        GameObject[] joints = skeletonManager.GetJoints();
        
        if (joints != null && joints.Length > 0)
        {
            InitializeEffects(joints);
            InitializeAnimationBlending(joints);
        }
    }
    
    void InitializeEffects(GameObject[] joints)
    {
        int jointCount = joints.Length;
        
        // Initialize arrays
        jointTrails = new TrailRenderer[jointCount];
        jointParticles = new ParticleSystem[jointCount];
        jointLights = new Light[jointCount];
        originalJointMaterials = new Material[jointCount];
        
        for (int i = 0; i < jointCount; i++)
        {
            if (joints[i] != null)
            {
                // Store original materials
                Renderer renderer = joints[i].GetComponent<Renderer>();
                if (renderer != null)
                {
                    originalJointMaterials[i] = renderer.material;
                }
                
                // Add trail renderer
                if (enableTrails)
                {
                    AddTrailRenderer(joints[i], i);
                }
                
                // Add particle system
                if (enableParticles)
                {
                    AddParticleSystem(joints[i], i);
                }
                
                // Add point light for glow effect
                if (enableGlow)
                {
                    AddPointLight(joints[i], i);
                }
            }
        }
        
        // Store original bone materials
        LineRenderer[] bones = skeletonManager.GetBones();
        if (bones != null)
        {
            originalBoneMaterials = new Material[bones.Length];
            for (int i = 0; i < bones.Length; i++)
            {
                if (bones[i] != null)
                {
                    originalBoneMaterials[i] = bones[i].material;
                }
            }
        }
    }
    
    void InitializeAnimationBlending(GameObject[] joints)
    {
        int jointCount = joints.Length;
        previousPositions = new Vector3[jointCount];
        currentPositions = new Vector3[jointCount];
        targetPositions = new Vector3[jointCount];
        
        for (int i = 0; i < jointCount; i++)
        {
            if (joints[i] != null)
            {
                Vector3 pos = joints[i].transform.localPosition;
                previousPositions[i] = pos;
                currentPositions[i] = pos;
                targetPositions[i] = pos;
            }
        }
    }
    
    void AddTrailRenderer(GameObject joint, int index)
    {
        TrailRenderer trail = joint.GetComponent<TrailRenderer>();
        if (trail == null)
        {
            trail = joint.AddComponent<TrailRenderer>();
        }
        
        trail.time = 1.0f;
        trail.startWidth = 0.1f;
        trail.endWidth = 0.01f;
        trail.material = trailMaterial != null ? trailMaterial : CreateDefaultTrailMaterial();
        
        // Color based on joint type
        Color trailColor = GetJointColor(index);
        trail.startColor = trailColor;
        trail.endColor = new Color(trailColor.r, trailColor.g, trailColor.b, 0f);
        
        jointTrails[index] = trail;
    }
    
    void AddParticleSystem(GameObject joint, int index)
    {
        ParticleSystem particles = joint.GetComponent<ParticleSystem>();
        if (particles == null)
        {
            particles = joint.AddComponent<ParticleSystem>();
        }
        
        var main = particles.main;
        main.startLifetime = 0.5f;
        main.startSpeed = 0.1f;
        main.startSize = 0.02f;
        main.startColor = GetJointColor(index);
        main.maxParticles = 10;
        
        var emission = particles.emission;
        emission.rateOverTime = 20f;
        
        var shape = particles.shape;
        shape.enabled = true;
        shape.shapeType = ParticleSystemShapeType.Sphere;
        shape.radius = 0.05f;
        
        jointParticles[index] = particles;
    }
    
    void AddPointLight(GameObject joint, int index)
    {
        Light pointLight = joint.GetComponent<Light>();
        if (pointLight == null)
        {
            pointLight = joint.AddComponent<Light>();
        }
        
        pointLight.type = LightType.Point;
        pointLight.color = GetJointColor(index);
        pointLight.intensity = 0.5f;
        pointLight.range = 0.5f;
        pointLight.shadows = LightShadows.None;
        
        jointLights[index] = pointLight;
    }
    
    Color GetJointColor(int jointIndex)
    {
        // Color coding based on body part
        if (jointIndex <= 10) // Head and face
            return Color.yellow;
        else if (jointIndex <= 16) // Arms
            return jointIndex % 2 == 0 ? Color.red : Color.blue;
        else if (jointIndex <= 22) // Hands
            return jointIndex % 2 == 0 ? Color.magenta : Color.cyan;
        else // Legs and feet
            return jointIndex % 2 == 0 ? Color.green : Color.white;
    }
    
    void SetupPostProcessing()
    {
        if (postProcessVolume == null)
        {
            postProcessVolume = FindObjectOfType<Volume>();
        }
        
        if (postProcessVolume != null && postProcessVolume.profile != null)
        {
            // Configure bloom
            if (postProcessVolume.profile.TryGet<Bloom>(out var bloom))
            {
                bloom.active = enableBloom;
                bloom.intensity.value = 0.3f;
                bloom.threshold.value = 1.0f;
            }
            
            // Configure color grading
            if (postProcessVolume.profile.TryGet<ColorAdjustments>(out var colorAdjustments))
            {
                colorAdjustments.active = enableColorGrading;
                colorAdjustments.contrast.value = 0.1f;
                colorAdjustments.saturation.value = 0.2f;
            }
            
            // Configure vignette
            if (postProcessVolume.profile.TryGet<Vignette>(out var vignette))
            {
                vignette.active = enableVignette;
                vignette.intensity.value = 0.2f;
            }
        }
    }
    
    void SetupLighting()
    {
        // Configure main light
        if (mainLight != null)
        {
            mainLight.intensity = lightIntensity;
            mainLight.color = Color.white;
        }
        
        // Set ambient lighting
        RenderSettings.ambientLight = ambientColor;
        RenderSettings.ambientMode = AmbientMode.Flat;
    }
    
    void Update()
    {
        UpdateVisualization();
        UpdateAnimationBlending();
        UpdateDynamicLighting();
        UpdatePerformanceStats();
    }
    
    void UpdateVisualization()
    {
        // Update material properties based on settings
        UpdateMaterials();
        
        // Update effect visibility
        UpdateEffectVisibility();
        
        // Update depth visualization
        if (enableDepthVisualization)
        {
            UpdateDepthVisualization();
        }
    }
    
    void UpdateMaterials()
    {
        GameObject[] joints = skeletonManager?.GetJoints();
        if (joints == null) return;
        
        for (int i = 0; i < joints.Length; i++)
        {
            if (joints[i] != null)
            {
                Renderer renderer = joints[i].GetComponent<Renderer>();
                if (renderer != null)
                {
                    if (enableGlow && glowMaterial != null)
                    {
                        renderer.material = glowMaterial;
                    }
                    else if (jointMaterial != null)
                    {
                        renderer.material = jointMaterial;
                    }
                    else if (originalJointMaterials[i] != null)
                    {
                        renderer.material = originalJointMaterials[i];
                    }
                }
            }
        }
        
        // Update bone materials
        LineRenderer[] bones = skeletonManager?.GetBones();
        if (bones != null)
        {
            for (int i = 0; i < bones.Length; i++)
            {
                if (bones[i] != null)
                {
                    if (boneMaterial != null)
                    {
                        bones[i].material = boneMaterial;
                    }
                    else if (originalBoneMaterials[i] != null)
                    {
                        bones[i].material = originalBoneMaterials[i];
                    }
                }
            }
        }
    }
    
    void UpdateEffectVisibility()
    {
        // Update trails
        if (jointTrails != null)
        {
            foreach (var trail in jointTrails)
            {
                if (trail != null)
                {
                    trail.enabled = enableTrails;
                }
            }
        }
        
        // Update particles
        if (jointParticles != null)
        {
            foreach (var particles in jointParticles)
            {
                if (particles != null)
                {
                    var emission = particles.emission;
                    emission.enabled = enableParticles;
                }
            }
        }
        
        // Update lights
        if (jointLights != null)
        {
            foreach (var light in jointLights)
            {
                if (light != null)
                {
                    light.enabled = enableGlow;
                }
            }
        }
    }
    
    void UpdateAnimationBlending()
    {
        if (!enableAnimationBlending) return;
        
        GameObject[] joints = skeletonManager?.GetJoints();
        if (joints == null || targetPositions == null) return;
        
        // Update target positions
        for (int i = 0; i < joints.Length && i < targetPositions.Length; i++)
        {
            if (joints[i] != null)
            {
                targetPositions[i] = joints[i].transform.localPosition;
            }
        }
        
        // Blend animation
        blendProgress += Time.deltaTime * blendSpeed;
        if (blendProgress > 1f) blendProgress = 1f;
        
        float blendFactor = blendCurve.Evaluate(blendProgress);
        
        for (int i = 0; i < joints.Length && i < currentPositions.Length; i++)
        {
            if (joints[i] != null)
            {
                currentPositions[i] = Vector3.Lerp(previousPositions[i], targetPositions[i], blendFactor);
                joints[i].transform.localPosition = currentPositions[i];
            }
        }
        
        // Reset blend when animation changes significantly
        if (Vector3.Distance(targetPositions[0], previousPositions[0]) > 0.1f)
        {
            System.Array.Copy(currentPositions, previousPositions, currentPositions.Length);
            blendProgress = 0f;
        }
    }
    
    void UpdateDynamicLighting()
    {
        if (!dynamicLighting || mainLight == null) return;
        
        // Animate light based on motion intensity
        float motionIntensity = CalculateMotionIntensity();
        mainLight.intensity = Mathf.Lerp(lightIntensity * 0.5f, lightIntensity * 1.5f, motionIntensity);
        
        // Rotate light slowly
        mainLight.transform.Rotate(0, Time.deltaTime * 10f, 0);
    }
    
    float CalculateMotionIntensity()
    {
        if (currentPositions == null || previousPositions == null) return 0f;
        
        float totalMovement = 0f;
        for (int i = 0; i < currentPositions.Length; i++)
        {
            totalMovement += Vector3.Distance(currentPositions[i], previousPositions[i]);
        }
        
        return Mathf.Clamp01(totalMovement / 10f);
    }
    
    void UpdateDepthVisualization()
    {
        GameObject[] joints = skeletonManager?.GetJoints();
        if (joints == null) return;
        
        for (int i = 0; i < joints.Length; i++)
        {
            if (joints[i] != null)
            {
                // Color based on Z depth
                float depth = joints[i].transform.localPosition.z;
                Color depthColor = Color.Lerp(Color.blue, Color.red, (depth + 1f) / 2f);
                
                Renderer renderer = joints[i].GetComponent<Renderer>();
                if (renderer != null)
                {
                    renderer.material.color = depthColor;
                }
            }
        }
    }
    
    void UpdatePerformanceStats()
    {
        frameCount++;
        if (Time.time - lastFPSUpdate > 1f)
        {
            currentFPS = frameCount / (Time.time - lastFPSUpdate);
            frameCount = 0;
            lastFPSUpdate = Time.time;
        }
    }
    
    Material CreateDefaultTrailMaterial()
    {
        Material mat = new Material(Shader.Find("Sprites/Default"));
        mat.color = Color.white;
        return mat;
    }
    
    // Public control methods
    public void SetGlowEffect(bool enabled)
    {
        enableGlow = enabled;
    }
    
    public void SetTrailEffect(bool enabled)
    {
        enableTrails = enabled;
    }
    
    public void SetParticleEffect(bool enabled)
    {
        enableParticles = enabled;
    }
    
    public void SetAnimationBlending(bool enabled, float speed = 5f)
    {
        enableAnimationBlending = enabled;
        blendSpeed = speed;
    }
    
    public void SetLightIntensity(float intensity)
    {
        lightIntensity = intensity;
        if (mainLight != null)
        {
            mainLight.intensity = intensity;
        }
    }
    
    public float GetCurrentFPS()
    {
        return currentFPS;
    }
    
    void OnDestroy()
    {
        // Clean up created materials
        if (jointTrails != null)
        {
            foreach (var trail in jointTrails)
            {
                if (trail != null && trail.material != null)
                {
                    DestroyImmediate(trail.material);
                }
            }
        }
    }
}
