import cv2
import numpy as np
import json
import time
import threading
import queue
import argparse
import logging
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass, asdict
import socket
import struct

try:
    from cvzone.PoseModule import PoseDetector
    import mediapipe as mp
except ImportError as e:
    print(f"Required libraries not found: {e}")
    print("Please install: pip install cvzone mediapipe")
    exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('motion_capture.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class PoseFrame:
    """Data class for storing pose information"""
    timestamp: float
    frame_id: int
    landmarks: List[List[float]]  # [x, y, z, confidence] for each landmark
    bbox: Optional[List[float]] = None
    confidence: float = 0.0
    person_id: int = 0

@dataclass
class MotionCaptureConfig:
    """Configuration for motion capture system"""
    # Input settings
    input_source: str = "webcam"  # "webcam", "video", "ip_camera"
    video_path: str = "Video.mp4"
    camera_id: int = 0
    ip_camera_url: str = ""

    # Detection settings
    detection_confidence: float = 0.5
    tracking_confidence: float = 0.5
    max_num_poses: int = 2

    # Processing settings
    enable_smoothing: bool = True
    smoothing_factor: float = 0.7
    enable_filtering: bool = True
    confidence_threshold: float = 0.3

    # Output settings
    output_format: str = "json"  # "json", "txt", "both"
    output_file: str = "motion_data"
    save_video: bool = False
    video_output_path: str = "output_video.mp4"

    # Real-time settings
    enable_realtime: bool = False
    unity_host: str = "localhost"
    unity_port: int = 12345

    # Display settings
    show_preview: bool = True
    preview_width: int = 640
    preview_height: int = 480

class KalmanFilter:
    """Simple Kalman filter for pose smoothing"""
    def __init__(self, process_variance=1e-3, measurement_variance=1e-1):
        self.process_variance = process_variance
        self.measurement_variance = measurement_variance
        self.posteri_estimate = 0.0
        self.posteri_error_estimate = 1.0

    def update(self, measurement):
        # Prediction
        priori_estimate = self.posteri_estimate
        priori_error_estimate = self.posteri_error_estimate + self.process_variance

        # Update
        blending_factor = priori_error_estimate / (priori_error_estimate + self.measurement_variance)
        self.posteri_estimate = priori_estimate + blending_factor * (measurement - priori_estimate)
        self.posteri_error_estimate = (1 - blending_factor) * priori_error_estimate

        return self.posteri_estimate

class PoseSmoothing:
    """Handles pose smoothing and filtering"""
    def __init__(self, config: MotionCaptureConfig):
        self.config = config
        self.filters = {}  # Dictionary of Kalman filters for each landmark coordinate
        self.previous_poses = []
        self.max_history = 5

    def smooth_pose(self, landmarks: List[List[float]], person_id: int = 0) -> List[List[float]]:
        """Apply smoothing to pose landmarks"""
        if not self.config.enable_smoothing:
            return landmarks

        smoothed_landmarks = []

        for i, landmark in enumerate(landmarks):
            smoothed_landmark = []

            for j, coord in enumerate(landmark[:3]):  # x, y, z coordinates
                filter_key = f"{person_id}_{i}_{j}"

                if filter_key not in self.filters:
                    self.filters[filter_key] = KalmanFilter()

                smoothed_coord = self.filters[filter_key].update(coord)
                smoothed_landmark.append(smoothed_coord)

            # Keep confidence as is
            if len(landmark) > 3:
                smoothed_landmark.append(landmark[3])

            smoothed_landmarks.append(smoothed_landmark)

        return smoothed_landmarks

    def filter_low_confidence(self, landmarks: List[List[float]]) -> List[List[float]]:
        """Filter out landmarks with low confidence"""
        if not self.config.enable_filtering:
            return landmarks

        filtered_landmarks = []

        for landmark in landmarks:
            if len(landmark) > 3 and landmark[3] >= self.config.confidence_threshold:
                filtered_landmarks.append(landmark)
            else:
                # Use previous position or zero if no confidence
                filtered_landmarks.append([0.0, 0.0, 0.0, 0.0])

        return filtered_landmarks

class UnityConnector:
    """Handles real-time communication with Unity"""
    def __init__(self, host: str, port: int):
        self.host = host
        self.port = port
        self.socket = None
        self.connected = False

    def connect(self) -> bool:
        """Establish connection to Unity"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.host, self.port))
            self.connected = True
            logger.info(f"Connected to Unity at {self.host}:{self.port}")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to Unity: {e}")
            return False

    def send_pose_data(self, pose_frame: PoseFrame) -> bool:
        """Send pose data to Unity"""
        if not self.connected:
            return False

        try:
            data = json.dumps(asdict(pose_frame)).encode('utf-8')
            data_length = struct.pack('!I', len(data))
            self.socket.sendall(data_length + data)
            return True
        except Exception as e:
            logger.error(f"Failed to send data to Unity: {e}")
            self.connected = False
            return False

    def disconnect(self):
        """Close connection to Unity"""
        if self.socket:
            self.socket.close()
            self.connected = False

class MotionCaptureSystem:
    """Main motion capture system class"""

    def __init__(self, config: MotionCaptureConfig):
        self.config = config
        self.detector = PoseDetector(
            detectionCon=config.detection_confidence,
            trackCon=config.tracking_confidence
        )
        self.pose_smoothing = PoseSmoothing(config)
        self.unity_connector = None

        # Data storage
        self.pose_frames: List[PoseFrame] = []
        self.frame_queue = queue.Queue(maxsize=100)
        self.processing_thread = None
        self.capture_thread = None
        self.running = False

        # Video capture
        self.cap = None
        self.video_writer = None

        # Statistics
        self.total_frames = 0
        self.processed_frames = 0
        self.start_time = None

    def initialize_capture(self) -> bool:
        """Initialize video capture based on configuration"""
        try:
            if self.config.input_source == "webcam":
                self.cap = cv2.VideoCapture(self.config.camera_id)
            elif self.config.input_source == "video":
                self.cap = cv2.VideoCapture(self.config.video_path)
            elif self.config.input_source == "ip_camera":
                self.cap = cv2.VideoCapture(self.config.ip_camera_url)
            else:
                logger.error(f"Unknown input source: {self.config.input_source}")
                return False

            if not self.cap.isOpened():
                logger.error("Failed to open video capture")
                return False

            # Set capture properties
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.config.preview_width)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.config.preview_height)

            # Initialize video writer if needed
            if self.config.save_video:
                fourcc = cv2.VideoWriter_fourcc(*'mp4v')
                fps = self.cap.get(cv2.CAP_PROP_FPS) or 30
                self.video_writer = cv2.VideoWriter(
                    self.config.video_output_path,
                    fourcc,
                    fps,
                    (self.config.preview_width, self.config.preview_height)
                )

            logger.info(f"Video capture initialized: {self.config.input_source}")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize capture: {e}")
            return False

    def initialize_unity_connection(self) -> bool:
        """Initialize Unity connection if real-time mode is enabled"""
        if not self.config.enable_realtime:
            return True

        self.unity_connector = UnityConnector(
            self.config.unity_host,
            self.config.unity_port
        )
        return self.unity_connector.connect()

    def capture_frames(self):
        """Capture frames in separate thread"""
        while self.running:
            try:
                success, frame = self.cap.read()
                if not success:
                    if self.config.input_source == "video":
                        logger.info("Video playback completed")
                        break
                    else:
                        continue

                timestamp = time.time()

                if not self.frame_queue.full():
                    self.frame_queue.put((frame, timestamp, self.total_frames))
                    self.total_frames += 1
                else:
                    logger.warning("Frame queue full, dropping frame")

            except Exception as e:
                logger.error(f"Error in capture thread: {e}")
                break

    def process_frames(self):
        """Process frames in separate thread"""
        while self.running:
            try:
                if not self.frame_queue.empty():
                    frame, timestamp, frame_id = self.frame_queue.get(timeout=1.0)
                    self.process_single_frame(frame, timestamp, frame_id)
                    self.processed_frames += 1

            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"Error in processing thread: {e}")
                break

    def process_single_frame(self, frame: np.ndarray, timestamp: float, frame_id: int):
        """Process a single frame for pose detection"""
        try:
            # Detect poses
            frame_with_pose = self.detector.findPose(frame, draw=True)
            lm_list, bbox_info = self.detector.findPosition(frame_with_pose, draw=True)

            if bbox_info and lm_list:
                # Convert landmarks to our format
                landmarks = []
                for lm in lm_list:
                    # lm format: [id, x, y, z]
                    x = lm[1]
                    y = frame.shape[0] - lm[2]  # Flip Y coordinate
                    z = lm[3] if len(lm) > 3 else 0.0
                    confidence = 1.0  # CVZone doesn't provide confidence per landmark
                    landmarks.append([x, y, z, confidence])

                # Apply smoothing and filtering
                landmarks = self.pose_smoothing.filter_low_confidence(landmarks)
                landmarks = self.pose_smoothing.smooth_pose(landmarks)

                # Create pose frame
                pose_frame = PoseFrame(
                    timestamp=timestamp,
                    frame_id=frame_id,
                    landmarks=landmarks,
                    bbox=[bbox_info["bbox"][0], bbox_info["bbox"][1],
                          bbox_info["bbox"][2], bbox_info["bbox"][3]],
                    confidence=1.0,  # Overall confidence
                    person_id=0
                )

                # Store frame
                self.pose_frames.append(pose_frame)

                # Send to Unity if real-time mode is enabled
                if self.unity_connector and self.unity_connector.connected:
                    self.unity_connector.send_pose_data(pose_frame)

            # Display frame if preview is enabled
            if self.config.show_preview:
                self.display_frame(frame_with_pose, frame_id)

            # Save video frame if enabled
            if self.video_writer:
                self.video_writer.write(frame_with_pose)

        except Exception as e:
            logger.error(f"Error processing frame {frame_id}: {e}")

    def display_frame(self, frame: np.ndarray, frame_id: int):
        """Display frame with pose overlay"""
        # Add frame info
        info_text = f"Frame: {frame_id} | Processed: {self.processed_frames}"
        if self.start_time:
            fps = self.processed_frames / (time.time() - self.start_time)
            info_text += f" | FPS: {fps:.1f}"

        cv2.putText(frame, info_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

        # Add instructions
        instructions = [
            "Press 'q' to quit",
            "Press 's' to save data",
            "Press 'r' to reset",
            "Press 'p' to pause/resume"
        ]

        for i, instruction in enumerate(instructions):
            cv2.putText(frame, instruction, (10, frame.shape[0] - 80 + i * 20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

        cv2.imshow("Motion Capture", frame)

    def save_data(self):
        """Save captured pose data"""
        if not self.pose_frames:
            logger.warning("No pose data to save")
            return

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        try:
            if self.config.output_format in ["json", "both"]:
                json_file = f"{self.config.output_file}_{timestamp}.json"
                with open(json_file, 'w') as f:
                    json.dump([asdict(frame) for frame in self.pose_frames], f, indent=2)
                logger.info(f"Saved JSON data to {json_file}")

            if self.config.output_format in ["txt", "both"]:
                txt_file = f"{self.config.output_file}_{timestamp}.txt"
                with open(txt_file, 'w') as f:
                    for frame in self.pose_frames:
                        line = ""
                        for landmark in frame.landmarks:
                            line += f"{landmark[0]},{landmark[1]},{landmark[2]},"
                        f.write(line.rstrip(',') + '\n')
                logger.info(f"Saved text data to {txt_file}")

                # Also save to Unity-compatible format
                unity_file = "AnimationFile.txt"
                with open(unity_file, 'w') as f:
                    for frame in self.pose_frames:
                        line = ""
                        for landmark in frame.landmarks:
                            line += f"{landmark[0]},{landmark[1]},{landmark[2]},"
                        f.write(line + '\n')
                logger.info(f"Saved Unity-compatible data to {unity_file}")

        except Exception as e:
            logger.error(f"Error saving data: {e}")

    def run(self):
        """Main execution loop"""
        logger.info("Starting Motion Capture System")

        # Initialize systems
        if not self.initialize_capture():
            return False

        if not self.initialize_unity_connection():
            logger.warning("Unity connection failed, continuing without real-time mode")

        self.running = True
        self.start_time = time.time()

        # Start threads
        self.capture_thread = threading.Thread(target=self.capture_frames)
        self.processing_thread = threading.Thread(target=self.process_frames)

        self.capture_thread.start()
        self.processing_thread.start()

        # Main loop for handling user input
        paused = False

        try:
            while self.running:
                if self.config.show_preview:
                    key = cv2.waitKey(1) & 0xFF

                    if key == ord('q'):
                        logger.info("Quit requested by user")
                        break
                    elif key == ord('s'):
                        logger.info("Saving data...")
                        self.save_data()
                    elif key == ord('r'):
                        logger.info("Resetting data...")
                        self.pose_frames.clear()
                        self.processed_frames = 0
                        self.start_time = time.time()
                    elif key == ord('p'):
                        paused = not paused
                        logger.info(f"{'Paused' if paused else 'Resumed'}")
                else:
                    time.sleep(0.1)

        except KeyboardInterrupt:
            logger.info("Interrupted by user")

        # Cleanup
        self.cleanup()
        return True

    def cleanup(self):
        """Clean up resources"""
        logger.info("Cleaning up...")

        self.running = False

        # Wait for threads to finish
        if self.capture_thread and self.capture_thread.is_alive():
            self.capture_thread.join(timeout=2.0)

        if self.processing_thread and self.processing_thread.is_alive():
            self.processing_thread.join(timeout=2.0)

        # Release resources
        if self.cap:
            self.cap.release()

        if self.video_writer:
            self.video_writer.release()

        if self.unity_connector:
            self.unity_connector.disconnect()

        cv2.destroyAllWindows()

        # Final save
        if self.pose_frames:
            self.save_data()

        # Print statistics
        if self.start_time:
            total_time = time.time() - self.start_time
            avg_fps = self.processed_frames / total_time if total_time > 0 else 0
            logger.info(f"Session complete: {self.processed_frames} frames processed in {total_time:.2f}s (avg {avg_fps:.1f} FPS)")

def create_config_from_args() -> MotionCaptureConfig:
    """Create configuration from command line arguments"""
    parser = argparse.ArgumentParser(description="Enhanced 3D Motion Capture System")

    # Input options
    parser.add_argument("--input", choices=["webcam", "video", "ip_camera"],
                       default="webcam", help="Input source type")
    parser.add_argument("--video-path", default="Video.mp4",
                       help="Path to video file")
    parser.add_argument("--camera-id", type=int, default=0,
                       help="Camera ID for webcam input")
    parser.add_argument("--ip-camera-url", default="",
                       help="URL for IP camera")

    # Detection settings
    parser.add_argument("--detection-confidence", type=float, default=0.5,
                       help="Pose detection confidence threshold")
    parser.add_argument("--tracking-confidence", type=float, default=0.5,
                       help="Pose tracking confidence threshold")
    parser.add_argument("--max-poses", type=int, default=2,
                       help="Maximum number of poses to detect")

    # Processing options
    parser.add_argument("--enable-smoothing", action="store_true", default=True,
                       help="Enable pose smoothing")
    parser.add_argument("--smoothing-factor", type=float, default=0.7,
                       help="Smoothing factor (0.0-1.0)")
    parser.add_argument("--enable-filtering", action="store_true", default=True,
                       help="Enable confidence filtering")
    parser.add_argument("--confidence-threshold", type=float, default=0.3,
                       help="Minimum confidence for landmarks")

    # Output options
    parser.add_argument("--output-format", choices=["json", "txt", "both"],
                       default="both", help="Output data format")
    parser.add_argument("--output-file", default="motion_data",
                       help="Output file prefix")
    parser.add_argument("--save-video", action="store_true",
                       help="Save processed video")
    parser.add_argument("--video-output", default="output_video.mp4",
                       help="Output video file path")

    # Real-time options
    parser.add_argument("--realtime", action="store_true",
                       help="Enable real-time Unity communication")
    parser.add_argument("--unity-host", default="localhost",
                       help="Unity host address")
    parser.add_argument("--unity-port", type=int, default=12345,
                       help="Unity port number")

    # Display options
    parser.add_argument("--no-preview", action="store_true",
                       help="Disable preview window")
    parser.add_argument("--preview-width", type=int, default=640,
                       help="Preview window width")
    parser.add_argument("--preview-height", type=int, default=480,
                       help="Preview window height")

    args = parser.parse_args()

    # Create configuration
    config = MotionCaptureConfig(
        input_source=args.input,
        video_path=args.video_path,
        camera_id=args.camera_id,
        ip_camera_url=args.ip_camera_url,
        detection_confidence=args.detection_confidence,
        tracking_confidence=args.tracking_confidence,
        max_num_poses=args.max_poses,
        enable_smoothing=args.enable_smoothing,
        smoothing_factor=args.smoothing_factor,
        enable_filtering=args.enable_filtering,
        confidence_threshold=args.confidence_threshold,
        output_format=args.output_format,
        output_file=args.output_file,
        save_video=args.save_video,
        video_output_path=args.video_output,
        enable_realtime=args.realtime,
        unity_host=args.unity_host,
        unity_port=args.unity_port,
        show_preview=not args.no_preview,
        preview_width=args.preview_width,
        preview_height=args.preview_height
    )

    return config

def main():
    """Main function"""
    print("Enhanced 3D Motion Capture System")
    print("=" * 40)

    try:
        # Create configuration
        config = create_config_from_args()

        # Log configuration
        logger.info("Configuration:")
        logger.info(f"  Input: {config.input_source}")
        if config.input_source == "video":
            logger.info(f"  Video path: {config.video_path}")
        elif config.input_source == "webcam":
            logger.info(f"  Camera ID: {config.camera_id}")
        logger.info(f"  Output format: {config.output_format}")
        logger.info(f"  Real-time mode: {config.enable_realtime}")
        logger.info(f"  Smoothing: {config.enable_smoothing}")

        # Create and run motion capture system
        motion_capture = MotionCaptureSystem(config)
        success = motion_capture.run()

        if success:
            logger.info("Motion capture completed successfully")
        else:
            logger.error("Motion capture failed")
            return 1

    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return 1

    return 0

if __name__ == "__main__":
    exit(main())