# Enhanced 3D Motion Capture System

A comprehensive, professional-grade 3D motion capture system that uses advanced computer vision to detect and track human poses in real-time. This enhanced version combines cutting-edge Python-based pose detection with sophisticated Unity 3D visualization to create a powerful, affordable motion capture solution using standard webcams.

## 🎯 Key Features

### Core Capabilities
- **Real-time Multi-Person Detection**: Advanced MediaPipe-based pose detection with multi-person tracking
- **Live 3D Visualization**: Real-time skeletal animation in Unity with advanced rendering
- **Professional Data Processing**: Kalman filtering, confidence scoring, and motion smoothing
- **Multiple Input Sources**: Webcam, video files, IP cameras, and streaming sources
- **Export Capabilities**: JSON, BVH, and custom formats for professional workflows

### Advanced Features
- **Inverse Kinematics (IK)**: Realistic bone movement with physics constraints
- **Motion Analysis**: Comprehensive analytics including motion intensity and quality metrics
- **Real-time Communication**: TCP/WebSocket streaming between Python and Unity
- **GUI Interface**: User-friendly interface for easy operation and configuration
- **Configuration Management**: Comprehensive settings system with presets and validation
- **Performance Optimization**: Multi-threading, GPU acceleration, and adaptive quality

## 🛠️ Technology Stack

### Core Technologies
- **Python 3.7+**: Advanced pose detection and data processing
- **OpenCV 4.8+**: Computer vision and video processing
- **MediaPipe**: Google's state-of-the-art pose detection
- **NumPy/SciPy**: Mathematical computations and signal processing
- **Unity 2021.3+**: Professional 3D visualization and animation

### Advanced Components
- **Kalman Filtering**: Motion smoothing and prediction
- **FABRIK Algorithm**: Inverse kinematics for realistic movement
- **TCP/WebSocket**: Real-time communication protocols
- **JSON/BVH Export**: Industry-standard motion data formats
- **Multi-threading**: Performance optimization and parallel processing

## 📋 System Requirements

### Hardware Requirements
- **CPU**: Intel i5 or AMD Ryzen 5 (minimum), i7/Ryzen 7 recommended
- **RAM**: 8GB minimum, 16GB recommended
- **GPU**: DirectX 11 compatible (for Unity), CUDA support optional
- **Camera**: USB webcam (720p minimum, 1080p recommended)
- **Storage**: 2GB free space for installation, additional space for recordings

### Software Requirements
- **Operating System**: Windows 10/11, macOS 10.15+, or Ubuntu 18.04+
- **Python**: 3.7 or higher with pip
- **Unity**: 2021.3 LTS or later
- **Visual Studio Code**: Recommended for development (optional)

## 🚀 Quick Start Installation

### Automated Setup (Recommended)
1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd "3D Motion Capture using Normal Webcam"
   ```

2. **Run the setup script**
   ```bash
   python setup.py
   ```
   This will automatically:
   - Install all Python dependencies
   - Create necessary directories
   - Set up default configurations
   - Create launch scripts
   - Test system compatibility

3. **Launch the application**
   - **GUI Mode**: Double-click `launch_gui.bat` (Windows) or `./launch_gui.sh` (Linux/Mac)
   - **Command Line**: Use `launch_cli.bat` or `./launch_cli.sh`

### Manual Installation
1. **Install Python dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Set up Unity Project**
   - Open Unity Hub
   - Click "Open" and select the `MotionCapture` folder
   - Wait for Unity to import all assets
   - Install Newtonsoft.Json package via Package Manager

3. **Create directories**
   ```bash
   mkdir output configs logs data exports
   ```

## 📖 Usage Guide

### Method 1: GUI Application (Recommended)

1. **Launch the GUI**
   ```bash
   python motion_capture_gui.py
   ```

2. **Configure Settings**
   - **Capture Tab**: Select input source (webcam/video/IP camera)
   - **Settings Tab**: Adjust detection confidence, smoothing, and output options
   - **Analysis Tab**: Analyze existing motion data

3. **Start Capturing**
   - Click "Start Capture" to begin real-time detection
   - Use keyboard shortcuts: 'S' to save, 'R' to reset, 'P' to pause
   - Monitor real-time statistics and preview

### Method 2: Command Line Interface

1. **Basic Usage**
   ```bash
   # Real-time webcam capture
   python MotionCap.py --input webcam

   # Process video file
   python MotionCap.py --input video --video-path your_video.mp4

   # Enable real-time Unity connection
   python MotionCap.py --realtime --unity-host localhost --unity-port 12345
   ```

2. **Advanced Options**
   ```bash
   # High-quality capture with smoothing
   python MotionCap.py --detection-confidence 0.7 --enable-smoothing --smoothing-factor 0.8

   # Multi-person detection
   python MotionCap.py --max-poses 3 --output-format json

   # Save processed video
   python MotionCap.py --save-video --video-output processed_output.mp4
   ```

### Method 3: Unity Real-time Visualization

1. **Setup Unity Scene**
   - Open Unity and load the MotionCapture project
   - Open SampleScene or create a new scene
   - Add RealTimeReceiver script to a GameObject
   - Configure the skeleton using SkeletonManager

2. **Configure Real-time Connection**
   - Set the port number (default: 12345)
   - Assign body joint GameObjects to the Body array
   - Enable smoothing and adjust scale factors

3. **Start Real-time Capture**
   - Press Play in Unity
   - Run Python script with `--realtime` flag
   - Watch live pose data stream into Unity

## 📁 Project Structure

```
├── Core Python Scripts
│   ├── MotionCap.py                    # Enhanced main motion capture system
│   ├── advanced_pose_detector.py       # Multi-person pose detection with tracking
│   ├── holistic_detector.py           # Combined pose, hand, and face detection
│   ├── multi_camera_system.py         # Multi-camera 3D triangulation system
│   ├── data_processor.py              # Advanced data analysis and processing
│   ├── config_manager.py              # Comprehensive configuration management
│   └── export_system.py               # Multi-format export (BVH, FBX, Unity)
│
├── GUI and Interface
│   ├── motion_capture_gui.py           # User-friendly GUI application
│   ├── setup.py                       # Automated installation script
│   └── test_system.py                 # Comprehensive system testing
│
├── Unity Project
│   └── MotionCapture/
│       ├── Assets/
│       │   ├── Scripts/
│       │   │   ├── AnimationCode.cs           # Enhanced animation controller
│       │   │   ├── LineCode.cs               # Advanced skeletal visualization
│       │   │   ├── RealTimeReceiver.cs       # Real-time data receiver
│       │   │   ├── SkeletonManager.cs        # Comprehensive skeleton management
│       │   │   ├── IKSystem.cs               # Inverse kinematics system
│       │   │   └── VisualizationController.cs # Advanced visual effects
│       │   ├── Scenes/                       # Unity scenes
│       │   └── Materials/                    # Rendering materials
│       └── ProjectSettings/                  # Unity project configuration
│
├── Configuration and Data
│   ├── configs/                        # Configuration files
│   ├── output/                         # Generated motion data
│   ├── exports/                        # Exported animations (BVH, FBX)
│   ├── logs/                          # System logs
│   └── motion_library/                # Organized motion capture library
│
├── Documentation and Setup
│   ├── README.md                       # This comprehensive guide
│   ├── requirements.txt               # Python dependencies
│   ├── launch_gui.bat/.sh            # GUI launcher scripts
│   └── launch_cli.bat/.sh            # Command-line launcher scripts
```

## 🔧 Advanced Configuration

### Configuration Management
The system uses a comprehensive configuration management system:

```bash
# View current configuration
python config_manager.py

# Edit configuration files
configs/default_config.json    # System defaults
configs/user_config.json       # User customizations
```

### Key Configuration Categories

#### Camera Settings
- **Resolution**: 640x480 to 1920x1080
- **Frame Rate**: 15-60 FPS
- **Multiple Cameras**: Support for up to 8 cameras
- **Calibration**: Automatic camera calibration for multi-camera setups

#### Detection Parameters
- **Model Complexity**: 0 (lite), 1 (full), 2 (heavy)
- **Confidence Thresholds**: Detection (0.5) and tracking (0.5)
- **Multi-person**: Up to 10 people simultaneously
- **Holistic Mode**: Enable face and hand detection

#### Processing Options
- **Kalman Filtering**: Advanced motion smoothing
- **Outlier Detection**: Automatic bad frame filtering
- **Real-time Processing**: Live streaming to Unity
- **Data Export**: Multiple format support

#### Unity Visualization
- **IK Constraints**: Realistic bone movement
- **Visual Effects**: Glow, trails, particles
- **Animation Blending**: Smooth transitions
- **Performance Optimization**: Adaptive quality

## 📊 System Architecture

### Advanced Processing Pipeline

1. **Multi-Modal Detection**
   - **Pose**: 33 body landmarks with confidence scoring
   - **Hands**: 21 landmarks per hand with gesture recognition
   - **Face**: 468 facial landmarks with expression analysis
   - **Multi-person**: Simultaneous tracking of multiple subjects

2. **Advanced Data Processing**
   - **Kalman Filtering**: Predictive smoothing for natural movement
   - **Confidence Filtering**: Automatic removal of low-quality data
   - **Outlier Detection**: Statistical analysis to remove anomalies
   - **Motion Analysis**: Intensity, smoothness, and quality metrics

3. **Real-time Communication**
   - **TCP/WebSocket**: Low-latency streaming to Unity
   - **Data Compression**: Efficient bandwidth usage
   - **Synchronization**: Frame-accurate timing
   - **Error Recovery**: Automatic reconnection and buffering

4. **3D Reconstruction**
   - **Multi-camera Triangulation**: True 3D pose estimation
   - **Camera Calibration**: Automatic intrinsic/extrinsic calibration
   - **Depth Estimation**: Single-camera depth approximation
   - **Coordinate Transformation**: World-space positioning

5. **Unity Visualization**
   - **Inverse Kinematics**: Physics-based bone constraints
   - **Advanced Rendering**: PBR materials and lighting
   - **Visual Effects**: Particles, trails, and post-processing
   - **Performance Optimization**: LOD and adaptive quality

## 🎮 Advanced Controls & Features

### GUI Application Controls
- **Start/Stop Capture**: Real-time recording control
- **Save Data**: Export in multiple formats (JSON, BVH, FBX)
- **Settings Panel**: Live parameter adjustment
- **Analysis Tools**: Motion quality assessment
- **Preview Window**: Real-time visualization with overlays

### Command Line Interface
```bash
# Basic capture
python MotionCap.py --input webcam --realtime

# Advanced multi-person capture
python MotionCap.py --max-poses 3 --enable-smoothing --save-video

# Multi-camera setup
python multi_camera_system.py --cameras 0,1,2 --calibrate

# Holistic capture (pose + hands + face)
python holistic_detector.py --enable-gestures --enable-expressions
```

### Unity Controls
- **Real-time Mode**: Live pose streaming from Python
- **IK System**: Physics-based bone constraints
- **Visual Effects**: Customizable rendering pipeline
- **Animation Blending**: Smooth transitions between poses
- **Performance Monitoring**: FPS and quality metrics

## 🔍 Troubleshooting & Optimization

### Performance Optimization

#### For Real-time Capture
- **Reduce Resolution**: Use 640x480 for better performance
- **Lower Model Complexity**: Set to 0 for fastest detection
- **Disable Unnecessary Features**: Turn off face/hand detection if not needed
- **Adjust Frame Rate**: Limit to 15-20 FPS for smoother performance

#### For High Quality
- **Increase Resolution**: Use 1280x720 or higher
- **Higher Model Complexity**: Set to 2 for best accuracy
- **Enable All Features**: Use holistic detection for complete capture
- **Multi-camera Setup**: Use 2-4 cameras for better 3D reconstruction

### Common Issues & Solutions

#### Installation Issues
```bash
# Python dependency conflicts
pip install --upgrade pip
pip install -r requirements.txt --force-reinstall

# MediaPipe installation issues
pip install mediapipe --no-deps
pip install opencv-python

# Unity package issues
# Install Newtonsoft.Json via Package Manager
```

#### Runtime Issues
1. **Camera Access Denied**
   - Check camera permissions in system settings
   - Close other applications using the camera
   - Try different camera IDs (0, 1, 2...)

2. **Poor Detection Quality**
   - Improve lighting conditions
   - Ensure subject is fully visible
   - Remove background clutter
   - Use contrasting clothing

3. **Unity Connection Issues**
   - Check firewall settings for port 12345
   - Ensure Python and Unity are on same network
   - Verify IP address in configuration

4. **Performance Issues**
   - Close unnecessary applications
   - Use GPU acceleration if available
   - Reduce detection confidence thresholds
   - Enable frame skipping for real-time use

#### Data Quality Issues
- **Jittery Motion**: Increase smoothing factor (0.7-0.9)
- **Missing Landmarks**: Lower confidence threshold (0.3-0.4)
- **Tracking Loss**: Enable multi-person mode for better tracking
- **Synchronization Issues**: Check system clock and reduce frame rate

## 🚀 Advanced Use Cases

### Professional Applications
- **Game Development**: Character animation and motion reference
- **Film/VFX**: Pre-visualization and motion reference
- **Sports Analysis**: Biomechanical analysis and training
- **Medical Research**: Gait analysis and rehabilitation
- **Virtual Production**: Real-time character control

### Research Applications
- **Computer Vision**: Pose estimation algorithm development
- **Machine Learning**: Training data generation
- **Biomechanics**: Human movement analysis
- **Robotics**: Human-robot interaction studies
- **Psychology**: Behavioral analysis and gesture studies

### Educational Uses
- **Animation Courses**: Teaching character animation principles
- **Computer Science**: Computer vision and AI demonstrations
- **Sports Science**: Movement analysis and technique improvement
- **Art/Design**: Digital art and interactive installations

## 🧪 Testing & Validation

### Comprehensive Testing
```bash
# Run full system test
python test_system.py

# Test specific components
python -m pytest tests/

# Performance benchmarking
python benchmark.py --duration 60
```

### Quality Assurance
- **Automated Testing**: Unit tests for all major components
- **Performance Monitoring**: Real-time FPS and accuracy metrics
- **Data Validation**: Automatic quality checks and error detection
- **Cross-platform Testing**: Windows, macOS, and Linux compatibility

## 🤝 Contributing

We welcome contributions! Here's how to get started:

1. **Fork the repository**
2. **Set up development environment**
   ```bash
   git clone your-fork-url
   cd "3D Motion Capture using Normal Webcam"
   python setup.py
   ```
3. **Create feature branch** (`git checkout -b feature/AmazingFeature`)
4. **Make your changes** with proper testing
5. **Run tests** (`python test_system.py`)
6. **Commit changes** (`git commit -m 'Add AmazingFeature'`)
7. **Push to branch** (`git push origin feature/AmazingFeature`)
8. **Open Pull Request** with detailed description

### Development Guidelines
- Follow PEP 8 style guidelines
- Add unit tests for new features
- Update documentation for API changes
- Test on multiple platforms when possible

## 📝 License

This project is open source and available under the [MIT License](LICENSE).

## 🙏 Acknowledgments

- **Google MediaPipe**: State-of-the-art pose detection framework
- **OpenCV Community**: Computer vision foundation
- **Unity Technologies**: Professional 3D engine and tools
- **Scientific Community**: Research papers and algorithms that made this possible

## 📧 Support & Community

- **GitHub Issues**: Bug reports and feature requests
- **Discussions**: Community support and sharing
- **Documentation**: Comprehensive guides and tutorials
- **Examples**: Sample projects and use cases

---

## 🎯 Future Roadmap

### Planned Features
- **AI-Enhanced Tracking**: Machine learning for improved accuracy
- **Cloud Processing**: Distributed computing for complex scenes
- **Mobile Support**: iOS and Android applications
- **VR/AR Integration**: Mixed reality applications
- **Professional Tools**: Advanced editing and post-processing

### Research Directions
- **Neural Pose Estimation**: Deep learning approaches
- **Multi-modal Fusion**: Combining multiple sensor types
- **Real-time Optimization**: Edge computing and optimization
- **Accessibility Features**: Tools for users with disabilities

---

**Note**: This enhanced system represents a significant advancement over traditional motion capture solutions, offering professional-grade features at a fraction of the cost. While suitable for many professional applications, specialized hardware may still be required for the highest precision use cases.
