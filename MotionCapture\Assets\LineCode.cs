using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class LineCode : MonoBehaviour
{
    [Header("Line Settings")]
    public Transform origin;
    public Transform destination;
    public float lineWidth = 0.05f;
    public Color lineColor = Color.white;
    public Material lineMaterial;

    [Header("Animation")]
    public bool animateWidth = false;
    public float minWidth = 0.02f;
    public float maxWidth = 0.08f;
    public float animationSpeed = 2f;

    [Header("Distance-based Effects")]
    public bool useDistanceColor = false;
    public Color nearColor = Color.green;
    public Color farColor = Color.red;
    public float maxDistance = 2f;

    private LineRenderer lineRenderer;
    private float originalWidth;
    private Color originalColor;

    void Start()
    {
        InitializeLineRenderer();
    }

    void InitializeLineRenderer()
    {
        lineRenderer = GetComponent<LineRenderer>();

        if (lineRenderer == null)
        {
            lineRenderer = gameObject.AddComponent<LineRenderer>();
        }

        // Configure line renderer
        lineRenderer.positionCount = 2;
        lineRenderer.startWidth = lineWidth;
        lineRenderer.endWidth = lineWidth;
        lineRenderer.color = lineColor;
        lineRenderer.useWorldSpace = true;

        // Set material if provided
        if (lineMaterial != null)
        {
            lineRenderer.material = lineMaterial;
        }
        else
        {
            // Create default material
            lineRenderer.material = CreateDefaultMaterial();
        }

        // Store original values
        originalWidth = lineWidth;
        originalColor = lineColor;
    }

    Material CreateDefaultMaterial()
    {
        Material mat = new Material(Shader.Find("Sprites/Default"));
        mat.color = lineColor;
        return mat;
    }

    void Update()
    {
        if (origin != null && destination != null && lineRenderer != null)
        {
            UpdateLinePositions();
            UpdateLineAppearance();
        }
    }

    void UpdateLinePositions()
    {
        lineRenderer.SetPosition(0, origin.position);
        lineRenderer.SetPosition(1, destination.position);
    }

    void UpdateLineAppearance()
    {
        // Animate width if enabled
        if (animateWidth)
        {
            float animatedWidth = Mathf.Lerp(minWidth, maxWidth,
                (Mathf.Sin(Time.time * animationSpeed) + 1f) / 2f);
            lineRenderer.startWidth = animatedWidth;
            lineRenderer.endWidth = animatedWidth;
        }
        else
        {
            lineRenderer.startWidth = lineWidth;
            lineRenderer.endWidth = lineWidth;
        }

        // Update color based on distance if enabled
        if (useDistanceColor)
        {
            float distance = Vector3.Distance(origin.position, destination.position);
            float normalizedDistance = Mathf.Clamp01(distance / maxDistance);
            Color currentColor = Color.Lerp(nearColor, farColor, normalizedDistance);
            lineRenderer.color = currentColor;
        }
        else
        {
            lineRenderer.color = lineColor;
        }
    }

    // Public methods for external control
    public void SetLineWidth(float width)
    {
        lineWidth = width;
        if (lineRenderer != null)
        {
            lineRenderer.startWidth = width;
            lineRenderer.endWidth = width;
        }
    }

    public void SetLineColor(Color color)
    {
        lineColor = color;
        if (lineRenderer != null)
        {
            lineRenderer.color = color;
        }
    }

    public void SetOrigin(Transform newOrigin)
    {
        origin = newOrigin;
    }

    public void SetDestination(Transform newDestination)
    {
        destination = newDestination;
    }

    public void SetLineMaterial(Material material)
    {
        lineMaterial = material;
        if (lineRenderer != null)
        {
            lineRenderer.material = material;
        }
    }

    public float GetLineLength()
    {
        if (origin != null && destination != null)
        {
            return Vector3.Distance(origin.position, destination.position);
        }
        return 0f;
    }

    public void EnableLine(bool enable)
    {
        if (lineRenderer != null)
        {
            lineRenderer.enabled = enable;
        }
    }

    // Reset to original settings
    public void ResetToDefaults()
    {
        lineWidth = originalWidth;
        lineColor = originalColor;
        animateWidth = false;
        useDistanceColor = false;

        if (lineRenderer != null)
        {
            lineRenderer.startWidth = lineWidth;
            lineRenderer.endWidth = lineWidth;
            lineRenderer.color = lineColor;
        }
    }
}
