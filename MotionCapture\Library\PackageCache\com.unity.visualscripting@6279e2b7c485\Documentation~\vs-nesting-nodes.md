# Nesting nodes

Use the following nodes to work with nesting Subgraphs and State Units in a Script Graph. 

For more information on Subgraphs and State Units, see [Subgraphs and State Units](vs-nesting-subgraphs-state-units.md).

|**Node** | **Description** |
| :----   | :-------------- |
| [**Input node**](vs-nesting-input-node.md) | Use an Input node to control the flow of logic and data from a Script Graph's Subgraph node. An Input node takes data from a parent graph and makes it available to a Subgraph. |
| [**Output node**](vs-nesting-output-node.md) | Use an Output node to control the flow of logic and data from a Script Graph's Subgraph node. An Output node sends data from a Subgraph and makes it available to a parent graph. |
| [**State Unit node**](vs-nesting-state-unit-node.md) | Use a State Unit node like a Subgraph. The node references and triggers a State Graph as a State Unit inside a Script Graph. |
| [**Subgraph node**](vs-nesting-subgraph-node.md) | Use a Subgraph node to reference and trigger another Script Graph's logic from inside a parent Script Graph. |
