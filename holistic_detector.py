"""
Holistic Detection System
Combines pose, hand, and face detection for comprehensive motion capture
"""

import cv2
import numpy as np
import mediapipe as mp
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
import time
import json

@dataclass
class HolisticDetection:
    """Complete holistic detection result"""
    timestamp: float
    pose_landmarks: Optional[List[List[float]]] = None
    face_landmarks: Optional[List[List[float]]] = None
    left_hand_landmarks: Optional[List[List[float]]] = None
    right_hand_landmarks: Optional[List[List[float]]] = None
    pose_confidence: float = 0.0
    face_confidence: float = 0.0
    left_hand_confidence: float = 0.0
    right_hand_confidence: float = 0.0

class HolisticDetector:
    """Advanced holistic detector for pose, hands, and face"""
    
    def __init__(self,
                 static_image_mode=False,
                 model_complexity=1,
                 enable_segmentation=False,
                 refine_face_landmarks=True,
                 min_detection_confidence=0.5,
                 min_tracking_confidence=0.5):
        
        self.mp_holistic = mp.solutions.holistic
        self.mp_drawing = mp.solutions.drawing_utils
        self.mp_drawing_styles = mp.solutions.drawing_styles
        
        # Initialize holistic model
        self.holistic = self.mp_holistic.Holistic(
            static_image_mode=static_image_mode,
            model_complexity=model_complexity,
            enable_segmentation=enable_segmentation,
            refine_face_landmarks=refine_face_landmarks,
            min_detection_confidence=min_detection_confidence,
            min_tracking_confidence=min_tracking_confidence
        )
        
        # Landmark indices for different parts
        self.pose_connections = self.mp_holistic.POSE_CONNECTIONS
        self.face_connections = self.mp_holistic.FACEMESH_CONTOURS
        self.hand_connections = self.mp_holistic.HAND_CONNECTIONS
        
        # Statistics
        self.detection_stats = {
            'total_frames': 0,
            'pose_detections': 0,
            'face_detections': 0,
            'left_hand_detections': 0,
            'right_hand_detections': 0
        }
    
    def detect_holistic(self, image: np.ndarray) -> HolisticDetection:
        """Detect pose, hands, and face in image"""
        self.detection_stats['total_frames'] += 1
        timestamp = time.time()
        
        # Convert BGR to RGB
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Process image
        results = self.holistic.process(rgb_image)
        
        # Extract landmarks
        detection = HolisticDetection(timestamp=timestamp)
        
        # Pose landmarks
        if results.pose_landmarks:
            detection.pose_landmarks = self._extract_pose_landmarks(results.pose_landmarks, image.shape)
            detection.pose_confidence = self._calculate_pose_confidence(detection.pose_landmarks)
            self.detection_stats['pose_detections'] += 1
        
        # Face landmarks
        if results.face_landmarks:
            detection.face_landmarks = self._extract_face_landmarks(results.face_landmarks, image.shape)
            detection.face_confidence = self._calculate_face_confidence(detection.face_landmarks)
            self.detection_stats['face_detections'] += 1
        
        # Left hand landmarks
        if results.left_hand_landmarks:
            detection.left_hand_landmarks = self._extract_hand_landmarks(results.left_hand_landmarks, image.shape)
            detection.left_hand_confidence = self._calculate_hand_confidence(detection.left_hand_landmarks)
            self.detection_stats['left_hand_detections'] += 1
        
        # Right hand landmarks
        if results.right_hand_landmarks:
            detection.right_hand_landmarks = self._extract_hand_landmarks(results.right_hand_landmarks, image.shape)
            detection.right_hand_confidence = self._calculate_hand_confidence(detection.right_hand_landmarks)
            self.detection_stats['right_hand_detections'] += 1
        
        return detection
    
    def _extract_pose_landmarks(self, pose_landmarks, image_shape) -> List[List[float]]:
        """Extract pose landmarks with coordinates and visibility"""
        landmarks = []
        height, width = image_shape[:2]
        
        for landmark in pose_landmarks.landmark:
            x = landmark.x * width
            y = landmark.y * height
            z = landmark.z * width  # Relative depth
            visibility = landmark.visibility
            
            landmarks.append([x, y, z, visibility])
        
        return landmarks
    
    def _extract_face_landmarks(self, face_landmarks, image_shape) -> List[List[float]]:
        """Extract face landmarks"""
        landmarks = []
        height, width = image_shape[:2]
        
        for landmark in face_landmarks.landmark:
            x = landmark.x * width
            y = landmark.y * height
            z = landmark.z * width  # Relative depth
            
            landmarks.append([x, y, z, 1.0])  # Face landmarks don't have visibility
        
        return landmarks
    
    def _extract_hand_landmarks(self, hand_landmarks, image_shape) -> List[List[float]]:
        """Extract hand landmarks"""
        landmarks = []
        height, width = image_shape[:2]
        
        for landmark in hand_landmarks.landmark:
            x = landmark.x * width
            y = landmark.y * height
            z = landmark.z * width  # Relative depth
            
            landmarks.append([x, y, z, 1.0])  # Hand landmarks don't have visibility
        
        return landmarks
    
    def _calculate_pose_confidence(self, landmarks: List[List[float]]) -> float:
        """Calculate pose confidence based on visibility scores"""
        if not landmarks:
            return 0.0
        
        visibilities = [lm[3] for lm in landmarks]
        return np.mean(visibilities)
    
    def _calculate_face_confidence(self, landmarks: List[List[float]]) -> float:
        """Calculate face confidence (simplified)"""
        if not landmarks:
            return 0.0
        
        # Face landmarks don't have visibility, so use presence as confidence
        return 1.0 if len(landmarks) > 400 else 0.5  # MediaPipe face has 468 landmarks
    
    def _calculate_hand_confidence(self, landmarks: List[List[float]]) -> float:
        """Calculate hand confidence (simplified)"""
        if not landmarks:
            return 0.0
        
        # Hand landmarks don't have visibility, so use presence as confidence
        return 1.0 if len(landmarks) == 21 else 0.5  # MediaPipe hand has 21 landmarks
    
    def draw_holistic(self, image: np.ndarray, detection: HolisticDetection, 
                     draw_pose=True, draw_face=True, draw_hands=True) -> np.ndarray:
        """Draw holistic detection results on image"""
        annotated_image = image.copy()
        
        # Convert landmarks back to MediaPipe format for drawing
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        results = self.holistic.process(rgb_image)
        
        # Draw pose
        if draw_pose and results.pose_landmarks:
            self.mp_drawing.draw_landmarks(
                annotated_image,
                results.pose_landmarks,
                self.pose_connections,
                landmark_drawing_spec=self.mp_drawing_styles.get_default_pose_landmarks_style()
            )
        
        # Draw face
        if draw_face and results.face_landmarks:
            self.mp_drawing.draw_landmarks(
                annotated_image,
                results.face_landmarks,
                self.face_connections,
                landmark_drawing_spec=None,
                connection_drawing_spec=self.mp_drawing_styles.get_default_face_mesh_contours_style()
            )
        
        # Draw hands
        if draw_hands:
            if results.left_hand_landmarks:
                self.mp_drawing.draw_landmarks(
                    annotated_image,
                    results.left_hand_landmarks,
                    self.hand_connections,
                    self.mp_drawing_styles.get_default_hand_landmarks_style(),
                    self.mp_drawing_styles.get_default_hand_connections_style()
                )
            
            if results.right_hand_landmarks:
                self.mp_drawing.draw_landmarks(
                    annotated_image,
                    results.right_hand_landmarks,
                    self.hand_connections,
                    self.mp_drawing_styles.get_default_hand_landmarks_style(),
                    self.mp_drawing_styles.get_default_hand_connections_style()
                )
        
        # Add confidence information
        y_offset = 30
        if detection.pose_landmarks:
            cv2.putText(annotated_image, f"Pose: {detection.pose_confidence:.2f}", 
                       (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            y_offset += 30
        
        if detection.face_landmarks:
            cv2.putText(annotated_image, f"Face: {detection.face_confidence:.2f}", 
                       (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 0), 2)
            y_offset += 30
        
        if detection.left_hand_landmarks:
            cv2.putText(annotated_image, f"Left Hand: {detection.left_hand_confidence:.2f}", 
                       (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
            y_offset += 30
        
        if detection.right_hand_landmarks:
            cv2.putText(annotated_image, f"Right Hand: {detection.right_hand_confidence:.2f}", 
                       (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)
        
        return annotated_image
    
    def analyze_gesture(self, hand_landmarks: List[List[float]]) -> str:
        """Analyze hand gesture (basic implementation)"""
        if not hand_landmarks or len(hand_landmarks) != 21:
            return "unknown"
        
        # Get key landmark positions
        thumb_tip = hand_landmarks[4]
        index_tip = hand_landmarks[8]
        middle_tip = hand_landmarks[12]
        ring_tip = hand_landmarks[16]
        pinky_tip = hand_landmarks[20]
        
        thumb_mcp = hand_landmarks[2]
        index_mcp = hand_landmarks[5]
        middle_mcp = hand_landmarks[9]
        ring_mcp = hand_landmarks[13]
        pinky_mcp = hand_landmarks[17]
        
        # Simple gesture recognition
        fingers_up = []
        
        # Thumb (different logic due to orientation)
        if thumb_tip[0] > thumb_mcp[0]:  # Right hand
            fingers_up.append(thumb_tip[0] > thumb_mcp[0])
        else:  # Left hand
            fingers_up.append(thumb_tip[0] < thumb_mcp[0])
        
        # Other fingers
        fingers_up.append(index_tip[1] < index_mcp[1])
        fingers_up.append(middle_tip[1] < middle_mcp[1])
        fingers_up.append(ring_tip[1] < ring_mcp[1])
        fingers_up.append(pinky_tip[1] < pinky_mcp[1])
        
        # Count fingers
        finger_count = sum(fingers_up)
        
        # Basic gesture classification
        if finger_count == 0:
            return "fist"
        elif finger_count == 1 and fingers_up[1]:
            return "pointing"
        elif finger_count == 2 and fingers_up[1] and fingers_up[2]:
            return "peace"
        elif finger_count == 5:
            return "open_hand"
        elif finger_count == 1 and fingers_up[0]:
            return "thumbs_up"
        else:
            return f"{finger_count}_fingers"
    
    def analyze_facial_expression(self, face_landmarks: List[List[float]]) -> str:
        """Analyze facial expression (basic implementation)"""
        if not face_landmarks or len(face_landmarks) < 468:
            return "unknown"
        
        # Key facial landmarks (MediaPipe face mesh indices)
        # Mouth corners
        left_mouth = face_landmarks[61]
        right_mouth = face_landmarks[291]
        mouth_center = face_landmarks[13]
        
        # Eyes
        left_eye_top = face_landmarks[159]
        left_eye_bottom = face_landmarks[145]
        right_eye_top = face_landmarks[386]
        right_eye_bottom = face_landmarks[374]
        
        # Eyebrows
        left_eyebrow = face_landmarks[70]
        right_eyebrow = face_landmarks[300]
        
        # Simple expression analysis
        mouth_width = abs(left_mouth[0] - right_mouth[0])
        mouth_height = abs(mouth_center[1] - (left_mouth[1] + right_mouth[1]) / 2)
        
        left_eye_height = abs(left_eye_top[1] - left_eye_bottom[1])
        right_eye_height = abs(right_eye_top[1] - right_eye_bottom[1])
        
        # Basic expression classification
        if mouth_width > 50 and mouth_height < 10:
            return "smile"
        elif mouth_height > 20:
            return "surprise"
        elif left_eye_height < 5 and right_eye_height < 5:
            return "eyes_closed"
        else:
            return "neutral"
    
    def export_holistic_data(self, detections: List[HolisticDetection], filename: str):
        """Export holistic detection data to JSON"""
        export_data = []
        
        for detection in detections:
            data = {
                'timestamp': detection.timestamp,
                'pose_landmarks': detection.pose_landmarks,
                'face_landmarks': detection.face_landmarks,
                'left_hand_landmarks': detection.left_hand_landmarks,
                'right_hand_landmarks': detection.right_hand_landmarks,
                'pose_confidence': detection.pose_confidence,
                'face_confidence': detection.face_confidence,
                'left_hand_confidence': detection.left_hand_confidence,
                'right_hand_confidence': detection.right_hand_confidence
            }
            export_data.append(data)
        
        with open(filename, 'w') as f:
            json.dump(export_data, f, indent=2)
    
    def get_statistics(self) -> Dict:
        """Get detection statistics"""
        total = self.detection_stats['total_frames']
        if total == 0:
            return self.detection_stats
        
        return {
            **self.detection_stats,
            'pose_detection_rate': self.detection_stats['pose_detections'] / total,
            'face_detection_rate': self.detection_stats['face_detections'] / total,
            'left_hand_detection_rate': self.detection_stats['left_hand_detections'] / total,
            'right_hand_detection_rate': self.detection_stats['right_hand_detections'] / total
        }
    
    def close(self):
        """Clean up resources"""
        if self.holistic:
            self.holistic.close()

# Example usage
def main():
    """Example usage of holistic detector"""
    detector = HolisticDetector()
    
    # Open video capture
    cap = cv2.VideoCapture(0)
    
    detections = []
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Detect holistic features
            detection = detector.detect_holistic(frame)
            detections.append(detection)
            
            # Analyze gestures and expressions
            if detection.left_hand_landmarks:
                gesture = detector.analyze_gesture(detection.left_hand_landmarks)
                print(f"Left hand gesture: {gesture}")
            
            if detection.right_hand_landmarks:
                gesture = detector.analyze_gesture(detection.right_hand_landmarks)
                print(f"Right hand gesture: {gesture}")
            
            if detection.face_landmarks:
                expression = detector.analyze_facial_expression(detection.face_landmarks)
                print(f"Facial expression: {expression}")
            
            # Draw results
            annotated_frame = detector.draw_holistic(frame, detection)
            
            # Display
            cv2.imshow('Holistic Detection', annotated_frame)
            
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
    
    finally:
        cap.release()
        cv2.destroyAllWindows()
        detector.close()
        
        # Export data
        if detections:
            detector.export_holistic_data(detections, 'holistic_data.json')
            print(f"Exported {len(detections)} detections")
        
        # Print statistics
        stats = detector.get_statistics()
        print(f"Detection statistics: {stats}")

if __name__ == "__main__":
    main()
