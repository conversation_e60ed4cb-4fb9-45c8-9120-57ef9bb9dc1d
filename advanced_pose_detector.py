"""
Advanced Pose Detection System
Supports multi-person detection, tracking, and advanced filtering
"""

import cv2
import numpy as np
import mediapipe as mp
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
import time
from collections import deque
import math

@dataclass
class PersonTracker:
    """Tracks a single person across frames"""
    person_id: int
    last_seen_frame: int
    position_history: deque
    confidence_history: deque
    bbox_history: deque
    kalman_filters: Dict[int, 'KalmanFilter2D']
    
    def __post_init__(self):
        self.kalman_filters = {}
        for i in range(33):  # 33 pose landmarks
            self.kalman_filters[i] = KalmanFilter2D()

@dataclass
class DetectedPose:
    """Single pose detection result"""
    person_id: int
    landmarks: List[List[float]]  # [x, y, z, visibility, presence]
    bbox: List[float]  # [x, y, width, height]
    confidence: float
    timestamp: float

class KalmanFilter2D:
    """2D Kalman filter for landmark smoothing"""
    def __init__(self, process_noise=1e-3, measurement_noise=1e-1):
        self.process_noise = process_noise
        self.measurement_noise = measurement_noise
        
        # State: [x, y, vx, vy]
        self.state = np.zeros(4)
        self.covariance = np.eye(4) * 1000
        
        # State transition matrix (constant velocity model)
        self.F = np.array([
            [1, 0, 1, 0],
            [0, 1, 0, 1],
            [0, 0, 1, 0],
            [0, 0, 0, 1]
        ])
        
        # Observation matrix
        self.H = np.array([
            [1, 0, 0, 0],
            [0, 1, 0, 0]
        ])
        
        # Process noise covariance
        self.Q = np.array([
            [0.25, 0, 0.5, 0],
            [0, 0.25, 0, 0.5],
            [0.5, 0, 1, 0],
            [0, 0.5, 0, 1]
        ]) * process_noise
        
        # Measurement noise covariance
        self.R = np.eye(2) * measurement_noise
        
        self.initialized = False
    
    def predict(self):
        """Predict next state"""
        self.state = self.F @ self.state
        self.covariance = self.F @ self.covariance @ self.F.T + self.Q
    
    def update(self, measurement):
        """Update with measurement"""
        if not self.initialized:
            self.state[:2] = measurement
            self.initialized = True
            return self.state[:2]
        
        # Predict
        self.predict()
        
        # Update
        y = measurement - self.H @ self.state  # Innovation
        S = self.H @ self.covariance @ self.H.T + self.R  # Innovation covariance
        K = self.covariance @ self.H.T @ np.linalg.inv(S)  # Kalman gain
        
        self.state = self.state + K @ y
        self.covariance = (np.eye(4) - K @ self.H) @ self.covariance
        
        return self.state[:2]

class AdvancedPoseDetector:
    """Advanced pose detection with multi-person tracking"""
    
    def __init__(self, 
                 max_num_poses=2,
                 min_detection_confidence=0.5,
                 min_tracking_confidence=0.5,
                 enable_segmentation=False):
        
        self.mp_pose = mp.solutions.pose
        self.mp_drawing = mp.solutions.drawing_utils
        self.mp_drawing_styles = mp.solutions.drawing_styles
        
        # Initialize pose detector
        self.pose = self.mp_pose.Pose(
            static_image_mode=False,
            model_complexity=1,
            enable_segmentation=enable_segmentation,
            min_detection_confidence=min_detection_confidence,
            min_tracking_confidence=min_tracking_confidence
        )
        
        # Multi-person tracking
        self.max_num_poses = max_num_poses
        self.person_trackers: Dict[int, PersonTracker] = {}
        self.next_person_id = 0
        self.max_tracking_distance = 100.0  # pixels
        self.max_frames_without_detection = 30
        
        # Frame counter
        self.frame_count = 0
        
        # Statistics
        self.detection_stats = {
            'total_detections': 0,
            'successful_tracks': 0,
            'lost_tracks': 0
        }
    
    def detect_poses(self, image: np.ndarray) -> List[DetectedPose]:
        """Detect poses in image with multi-person tracking"""
        self.frame_count += 1
        timestamp = time.time()
        
        # Convert BGR to RGB
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Process image
        results = self.pose.process(rgb_image)
        
        detected_poses = []
        
        if results.pose_landmarks:
            # Extract landmarks
            landmarks = self._extract_landmarks(results.pose_landmarks, image.shape)
            
            # Calculate bounding box
            bbox = self._calculate_bbox(landmarks)
            
            # Calculate overall confidence
            confidence = self._calculate_confidence(landmarks)
            
            # Track or assign person ID
            person_id = self._track_person(landmarks, bbox, confidence)
            
            # Apply Kalman filtering
            filtered_landmarks = self._apply_kalman_filtering(person_id, landmarks)
            
            detected_pose = DetectedPose(
                person_id=person_id,
                landmarks=filtered_landmarks,
                bbox=bbox,
                confidence=confidence,
                timestamp=timestamp
            )
            
            detected_poses.append(detected_pose)
            self.detection_stats['total_detections'] += 1
        
        # Clean up old trackers
        self._cleanup_trackers()
        
        return detected_poses
    
    def _extract_landmarks(self, pose_landmarks, image_shape) -> List[List[float]]:
        """Extract landmarks with visibility and presence scores"""
        landmarks = []
        height, width = image_shape[:2]
        
        for landmark in pose_landmarks.landmark:
            x = landmark.x * width
            y = landmark.y * height
            z = landmark.z * width  # Relative depth
            visibility = landmark.visibility
            presence = getattr(landmark, 'presence', 1.0)  # Not all versions have presence
            
            landmarks.append([x, y, z, visibility, presence])
        
        return landmarks
    
    def _calculate_bbox(self, landmarks: List[List[float]]) -> List[float]:
        """Calculate bounding box from landmarks"""
        if not landmarks:
            return [0, 0, 0, 0]
        
        x_coords = [lm[0] for lm in landmarks if lm[3] > 0.5]  # Only visible landmarks
        y_coords = [lm[1] for lm in landmarks if lm[3] > 0.5]
        
        if not x_coords or not y_coords:
            return [0, 0, 0, 0]
        
        x_min, x_max = min(x_coords), max(x_coords)
        y_min, y_max = min(y_coords), max(y_coords)
        
        # Add padding
        padding = 20
        x_min = max(0, x_min - padding)
        y_min = max(0, y_min - padding)
        width = x_max - x_min + 2 * padding
        height = y_max - y_min + 2 * padding
        
        return [x_min, y_min, width, height]
    
    def _calculate_confidence(self, landmarks: List[List[float]]) -> float:
        """Calculate overall pose confidence"""
        if not landmarks:
            return 0.0
        
        # Use visibility scores
        visibilities = [lm[3] for lm in landmarks]
        return np.mean(visibilities)
    
    def _track_person(self, landmarks: List[List[float]], bbox: List[float], confidence: float) -> int:
        """Track person across frames or assign new ID"""
        bbox_center = [bbox[0] + bbox[2]/2, bbox[1] + bbox[3]/2]
        
        # Find closest existing tracker
        best_match_id = None
        best_distance = float('inf')
        
        for person_id, tracker in self.person_trackers.items():
            if len(tracker.bbox_history) > 0:
                last_bbox = tracker.bbox_history[-1]
                last_center = [last_bbox[0] + last_bbox[2]/2, last_bbox[1] + last_bbox[3]/2]
                
                distance = math.sqrt(
                    (bbox_center[0] - last_center[0])**2 + 
                    (bbox_center[1] - last_center[1])**2
                )
                
                if distance < self.max_tracking_distance and distance < best_distance:
                    best_distance = distance
                    best_match_id = person_id
        
        # Use existing tracker or create new one
        if best_match_id is not None:
            person_id = best_match_id
        else:
            person_id = self.next_person_id
            self.next_person_id += 1
            
            # Create new tracker
            self.person_trackers[person_id] = PersonTracker(
                person_id=person_id,
                last_seen_frame=self.frame_count,
                position_history=deque(maxlen=30),
                confidence_history=deque(maxlen=30),
                bbox_history=deque(maxlen=30),
                kalman_filters={}
            )
        
        # Update tracker
        tracker = self.person_trackers[person_id]
        tracker.last_seen_frame = self.frame_count
        tracker.position_history.append([lm[:2] for lm in landmarks])
        tracker.confidence_history.append(confidence)
        tracker.bbox_history.append(bbox)
        
        return person_id
    
    def _apply_kalman_filtering(self, person_id: int, landmarks: List[List[float]]) -> List[List[float]]:
        """Apply Kalman filtering to landmarks"""
        tracker = self.person_trackers[person_id]
        filtered_landmarks = []
        
        for i, landmark in enumerate(landmarks):
            if i not in tracker.kalman_filters:
                tracker.kalman_filters[i] = KalmanFilter2D()
            
            # Only filter if landmark is visible
            if landmark[3] > 0.5:  # visibility threshold
                filtered_pos = tracker.kalman_filters[i].update(landmark[:2])
                filtered_landmarks.append([
                    filtered_pos[0], filtered_pos[1], landmark[2], 
                    landmark[3], landmark[4]
                ])
            else:
                # Use prediction for invisible landmarks
                tracker.kalman_filters[i].predict()
                predicted_pos = tracker.kalman_filters[i].state[:2]
                filtered_landmarks.append([
                    predicted_pos[0], predicted_pos[1], landmark[2],
                    0.0, landmark[4]  # Set visibility to 0
                ])
        
        return filtered_landmarks
    
    def _cleanup_trackers(self):
        """Remove old trackers that haven't been seen recently"""
        to_remove = []
        
        for person_id, tracker in self.person_trackers.items():
            frames_since_last_seen = self.frame_count - tracker.last_seen_frame
            
            if frames_since_last_seen > self.max_frames_without_detection:
                to_remove.append(person_id)
                self.detection_stats['lost_tracks'] += 1
        
        for person_id in to_remove:
            del self.person_trackers[person_id]
    
    def draw_poses(self, image: np.ndarray, detected_poses: List[DetectedPose]) -> np.ndarray:
        """Draw detected poses on image"""
        annotated_image = image.copy()
        
        # Define colors for different persons
        colors = [
            (255, 0, 0),    # Red
            (0, 255, 0),    # Green
            (0, 0, 255),    # Blue
            (255, 255, 0),  # Yellow
            (255, 0, 255),  # Magenta
            (0, 255, 255),  # Cyan
        ]
        
        for pose in detected_poses:
            color = colors[pose.person_id % len(colors)]
            
            # Draw landmarks
            for i, landmark in enumerate(pose.landmarks):
                if landmark[3] > 0.5:  # Only draw visible landmarks
                    x, y = int(landmark[0]), int(landmark[1])
                    cv2.circle(annotated_image, (x, y), 3, color, -1)
                    
                    # Draw landmark index for debugging
                    cv2.putText(annotated_image, str(i), (x+5, y-5), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.3, color, 1)
            
            # Draw bounding box
            bbox = pose.bbox
            x, y, w, h = int(bbox[0]), int(bbox[1]), int(bbox[2]), int(bbox[3])
            cv2.rectangle(annotated_image, (x, y), (x+w, y+h), color, 2)
            
            # Draw person ID and confidence
            cv2.putText(annotated_image, f"Person {pose.person_id}", 
                       (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
            cv2.putText(annotated_image, f"Conf: {pose.confidence:.2f}", 
                       (x, y+h+20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
        
        # Draw statistics
        stats_text = f"Detections: {len(detected_poses)} | Total: {self.detection_stats['total_detections']}"
        cv2.putText(annotated_image, stats_text, (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        return annotated_image
    
    def get_statistics(self) -> Dict:
        """Get detection statistics"""
        return {
            **self.detection_stats,
            'active_trackers': len(self.person_trackers),
            'frame_count': self.frame_count
        }
    
    def reset_tracking(self):
        """Reset all tracking data"""
        self.person_trackers.clear()
        self.next_person_id = 0
        self.frame_count = 0
        self.detection_stats = {
            'total_detections': 0,
            'successful_tracks': 0,
            'lost_tracks': 0
        }
    
    def close(self):
        """Clean up resources"""
        if self.pose:
            self.pose.close()

# Example usage
def main():
    """Example usage of AdvancedPoseDetector"""
    detector = AdvancedPoseDetector(max_num_poses=2)
    
    # Open video capture
    cap = cv2.VideoCapture(0)  # Use webcam
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Detect poses
            poses = detector.detect_poses(frame)
            
            # Draw results
            annotated_frame = detector.draw_poses(frame, poses)
            
            # Display
            cv2.imshow('Advanced Pose Detection', annotated_frame)
            
            # Print statistics every 100 frames
            if detector.frame_count % 100 == 0:
                stats = detector.get_statistics()
                print(f"Statistics: {stats}")
            
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
    
    finally:
        cap.release()
        cv2.destroyAllWindows()
        detector.close()

if __name__ == "__main__":
    main()
