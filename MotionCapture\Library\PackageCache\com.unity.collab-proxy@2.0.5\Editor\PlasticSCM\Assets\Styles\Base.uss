.collapse {
    display: none;
}

.hide {
    visibility: hidden;
}

.row {
    flex-direction: row;
}

.row-reverse {
    flex-direction: row-reverse;
}

.column {
    flex-direction: column;
}

.flex-container {
    display: flex;
}

.grow {
    flex-grow: 1;
}

.grow-max {
    flex-grow: 10;
}

.parent-vertical-center{
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.elements-vertical-center{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.horizontally-centered {
    justify-content: center;
}

.align-end {
    justify-content: flex-end;
}

.align-center{
    justify-content: center;
}

.align-vertical-center{
    flex-direction: row;
    align-items: center;
}

.align-start {
    justify-content: flex-start;
}

.main {
    margin-top: 10px;
    padding-left: 25px;
    padding-right: 20px;
}

.title {
    margin: 10px 0px;
    font-size: 18px;
}

.error {
    color: #FF0000;
}

.anchor {
    color: #2196F3;
    padding: 0px;
    margin: 0px;
    background-color: transparent;
    border-width: 0px;
}

.classic-button {
    padding: 7px;
    margin-top: 10px;
    height: 30px;
}

.alert-label {
    color: red;
}

.footer {
    position: absolute;
    margin: 20px 10px 0px 10px;
    bottom: 5px;
}

.container {
    padding: 10px;
    border-bottom-width: 1px;
    border-bottom-color: #999999;
}

.container.last {
    border-bottom-width: 0px;
}

.sub-section {
    margin-left: 10px;
}

Button {
    cursor: link;
}

Label {
    white-space: normal;
}

TextField {
    margin-left: 0px;
    margin-top: 3px;
    margin-right: 10px;
    margin-bottom: 3px;
}

Toggle {
    margin-left: 0px;
}